/**
 * TaskRunner 测试文件
 * 用于验证长期运行优化的效果
 */

import { TaskRunner } from '../services/TaskRunner.js';

class TaskRunnerTester {
    constructor() {
        this.testResults = [];
        this.taskRunner = null;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('开始 TaskRunner 长期运行测试...');
        
        try {
            await this.testMemoryManagement();
            await this.testQueueManagement();
            await this.testTimerManagement();
            await this.testErrorRecovery();
            await this.testHealthMonitoring();
            
            this.printTestResults();
        } catch (error) {
            console.error('测试运行失败:', error);
        }
    }

    /**
     * 测试内存管理
     */
    async testMemoryManagement() {
        console.log('测试内存管理...');
        
        this.taskRunner = new TaskRunner();
        
        // 测试队列大小限制
        for (let i = 0; i < 60; i++) {
            this.taskRunner.addToClickQueue({
                class_name: `test_${i}`,
                bbox: [10, 10, 20, 20],
                confidence: 0.8,
                recognitionX: 0,
                recognitionY: 0
            });
        }
        
        const queueSize = this.taskRunner.clickQueue.length;
        const passed = queueSize <= this.taskRunner.maxQueueSize;
        
        this.testResults.push({
            name: '队列大小限制',
            passed,
            details: `队列大小: ${queueSize}/${this.taskRunner.maxQueueSize}`
        });
        
        // 测试内存清理
        this.taskRunner.performMemoryCleanup();
        
        this.testResults.push({
            name: '内存清理',
            passed: true,
            details: '内存清理执行成功'
        });
    }

    /**
     * 测试队列管理
     */
    async testQueueManagement() {
        console.log('测试队列管理...');
        
        // 测试重复目标过滤
        const target1 = {
            class_name: 'test_target',
            bbox: [10, 10, 20, 20],
            confidence: 0.8,
            recognitionX: 0,
            recognitionY: 0
        };
        
        const target2 = {
            class_name: 'test_target',
            bbox: [12, 12, 22, 22], // 相近位置
            confidence: 0.9,
            recognitionX: 0,
            recognitionY: 0
        };
        
        this.taskRunner.clearClickQueue();
        this.taskRunner.addToClickQueue(target1);
        this.taskRunner.addToClickQueue(target2);
        
        const queueLength = this.taskRunner.clickQueue.length;
        const duplicateFilterPassed = queueLength === 1;
        
        this.testResults.push({
            name: '重复目标过滤',
            passed: duplicateFilterPassed,
            details: `队列长度: ${queueLength} (应为1)`
        });
        
        // 测试优先级排序
        this.taskRunner.clearClickQueue();
        const lowPriorityTarget = {
            class_name: 'low_priority',
            bbox: [10, 10, 20, 20],
            confidence: 0.5,
            recognitionX: 100,
            recognitionY: 100
        };
        
        const highPriorityTarget = {
            class_name: 'high_priority',
            bbox: [10, 10, 20, 20],
            confidence: 0.9,
            recognitionX: 0,
            recognitionY: 0
        };
        
        this.taskRunner.addToClickQueue(lowPriorityTarget);
        this.taskRunner.addToClickQueue(highPriorityTarget);
        
        const firstTarget = this.taskRunner.clickQueue[0];
        const priorityPassed = firstTarget.priority > lowPriorityTarget.priority;
        
        this.testResults.push({
            name: '优先级排序',
            passed: priorityPassed,
            details: `第一个目标优先级: ${firstTarget.priority}`
        });
    }

    /**
     * 测试定时器管理
     */
    async testTimerManagement() {
        console.log('测试定时器管理...');
        
        // 创建一些定时器
        const timeout1 = this.taskRunner.createTimeout(() => {}, 1000, 'test_timeout_1');
        const timeout2 = this.taskRunner.createTimeout(() => {}, 2000, 'test_timeout_2');
        const interval1 = this.taskRunner.createInterval(() => {}, 1000, 'test_interval_1');
        
        const timerStats = this.taskRunner.getTimerStats();
        const timerCreationPassed = timerStats.timeouts.count === 2 && timerStats.intervals.count === 1;
        
        this.testResults.push({
            name: '定时器创建',
            passed: timerCreationPassed,
            details: `Timeouts: ${timerStats.timeouts.count}, Intervals: ${timerStats.intervals.count}`
        });
        
        // 清理定时器
        this.taskRunner.clearAllTimers();
        const cleanupStats = this.taskRunner.getTimerStats();
        const timerCleanupPassed = cleanupStats.timeouts.count === 0 && cleanupStats.intervals.count === 0;
        
        this.testResults.push({
            name: '定时器清理',
            passed: timerCleanupPassed,
            details: `清理后 - Timeouts: ${cleanupStats.timeouts.count}, Intervals: ${cleanupStats.intervals.count}`
        });
    }

    /**
     * 测试错误恢复
     */
    async testErrorRecovery() {
        console.log('测试错误恢复...');
        
        // 模拟网络错误
        const networkError = new Error('network connection failed');
        const errorType = this.taskRunner.classifyError(networkError);
        const networkErrorPassed = errorType === 'network';
        
        this.testResults.push({
            name: '错误分类 - 网络错误',
            passed: networkErrorPassed,
            details: `错误类型: ${errorType}`
        });
        
        // 模拟超时错误
        const timeoutError = new Error('operation timeout');
        const timeoutType = this.taskRunner.classifyError(timeoutError);
        const timeoutErrorPassed = timeoutType === 'timeout';
        
        this.testResults.push({
            name: '错误分类 - 超时错误',
            passed: timeoutErrorPassed,
            details: `错误类型: ${timeoutType}`
        });
    }

    /**
     * 测试健康监控
     */
    async testHealthMonitoring() {
        console.log('测试健康监控...');
        
        // 测试健康检查
        const healthStatus = this.taskRunner.checkSystemHealth();
        const healthCheckPassed = healthStatus.overall !== undefined;
        
        this.testResults.push({
            name: '健康状态检查',
            passed: healthCheckPassed,
            details: `健康状态: ${healthStatus.overall}, 问题数: ${healthStatus.issues.length}`
        });
        
        // 测试系统状态获取
        const systemStatus = this.taskRunner.getSystemStatus();
        const systemStatusPassed = systemStatus.health !== undefined && 
                                  systemStatus.performance !== undefined;
        
        this.testResults.push({
            name: '系统状态获取',
            passed: systemStatusPassed,
            details: `包含健康状态和性能数据`
        });
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n=== TaskRunner 测试结果 ===');
        
        let passedCount = 0;
        let totalCount = this.testResults.length;
        
        this.testResults.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.name}: ${result.details}`);
            if (result.passed) passedCount++;
        });
        
        console.log(`\n总计: ${passedCount}/${totalCount} 测试通过`);
        console.log(`成功率: ${Math.round(passedCount / totalCount * 100)}%`);
        
        if (passedCount === totalCount) {
            console.log('🎉 所有测试通过！TaskRunner 已优化完成。');
        } else {
            console.log('⚠️  部分测试失败，需要进一步优化。');
        }
        
        // 清理资源
        if (this.taskRunner) {
            this.taskRunner.stopTask();
        }
    }
}

// 导出测试类
export { TaskRunnerTester };

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location) {
    const tester = new TaskRunnerTester();
    tester.runAllTests();
}
