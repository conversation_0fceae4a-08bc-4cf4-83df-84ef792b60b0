import {sleep} from "@/services/RobotArmService";
import {ConfigWebsocketService} from "@/services/websocket/ConfigWebsocketService";
import {RobotArmSocket} from "@/services/websocket/RobotArmSocket";

/**
 * 校准服务类
 * 负责处理机械臂校准相关的逻辑
 */
export class CalibrationService {
    constructor() {
        this.isCalibrating = false;
        this.robotPos = {x: 0, y: 0};
        this.robotMoveSize = {x: 10, y: 10};
    }

    /**
     * 单轴退避算法：根据距离动态计算移动步长（返回整数）
     * @param successDistance
     * @param {number} distance - 当前位置与目标位置的距离
     * @returns {number} 移动步长（整数）
     */
    calculateSingleAxisBackoff(successDistance, distance) {

        const absDistance = Math.abs(distance);

        if (absDistance < successDistance) {
            return 0;
        }

        // 根据距离计算步长：距离越远，步长越大
        let stepSize;
        if (absDistance > 100) {
            stepSize = 5; // 距离很远时使用最大步长
        } else if (absDistance > 50) {
            stepSize = 3
        } else if (absDistance > 20) {
            stepSize = 2
        } else {
            stepSize = 1
        }

        // 确保返回整数
        return distance > 0 ? -stepSize : stepSize;
    }


    /**
     * 开始校准流程
     * @param {object} params - 校准参数
     * @param {object} params.selectedPoint - 目标点位
     * @param {string} params.deviceId - 设备ID
     * @param {object} params.websocketService - WebSocket服务实例
     * @param {array} params.clickEvents - 点击事件数组
     * @param {function} params.onCalibrationSuccess - 校准成功回调
     * @param {function} params.onCalibrationStop - 校准停止回调
     */
    async startCalibration(params) {
        const {
            selectedPoint,
            deviceId,
            websocketService,
            clickEvents,
            onCalibrationSuccess,
            onCalibrationStop
        } = params;

        this.isCalibrating = true;
        this.selectedPoint = selectedPoint;
        this.deviceId = deviceId;
        this.websocketService = websocketService;
        this.clickEvents = clickEvents;
        this.onCalibrationSuccess = onCalibrationSuccess;
        this.onCalibrationStop = onCalibrationStop;
        console.log('startCalibration')
        ConfigWebsocketService.getConfig(deviceId, websocketService, async (config) => {
            if (!config.physicalWidth || !config.physicalHeight) {
                uni.showModal({
                    title: '还未计算物理尺寸',
                    content: '请先计算物理尺寸',
                    showCancel: false,
                    success: () => {
                        uni.navigateTo({
                            url: '/pages/jz/size-pc'
                        }).then(_r => {
                        });
                    }
                });
                return;
            }

            this.robotPos.x = 0;
            this.robotPos.y = 0;

            // 机械臂复位
            await RobotArmSocket.notifyReset(this.websocketService, this.deviceId);
            await this.calibrationRun();
        });
    }

    /**
     * 校准运行逻辑
     */
    async calibrationRun() {
        console.log('calibrationRun');
        if (this.isCalibrating === false) {
            return;
        }

        // 目标点位
        const targetPoint = this.selectedPoint;

        // 检查是否有大于10个命中的数据
        const hitEvents = this.clickEvents.filter(event => event.isHit);
        if (hitEvents.length > 10) {
            // 找到最接近目标点位的机械臂位置
            let closestEvent = null;
            let minDistance = Infinity;

            hitEvents.forEach(event => {
                if (event.robotPos) {
                    const distance = Math.sqrt(
                        Math.pow(event.x - targetPoint.x, 2) +
                        Math.pow(event.y - targetPoint.y, 2)
                    );
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestEvent = event;
                    }
                }
            });

            if (closestEvent && closestEvent.robotPos) {
                // 移动机械臂到最接近的位置
                this.robotPos.x = closestEvent.robotPos.x;
                this.robotPos.y = closestEvent.robotPos.y;
                await RobotArmSocket.notifyClick(this.websocketService, this.deviceId, this.robotPos.x, this.robotPos.y);
                await sleep(1000);

                // 调用校准成功并结束
                await this.calibrationSuccess();
                this.stopCalibration();
                return;
            }
        }

        // 执行机械臂移动
        this.robotPos.x += this.robotMoveSize.x;
        this.robotPos.y += this.robotMoveSize.y;

        console.log('机械臂移动到：', this.robotPos);
        // 机械臂移动到新位置
        await RobotArmSocket.notifyClick(this.websocketService, this.deviceId, this.robotPos.x, this.robotPos.y);
        await sleep(2000);

        // 获取最后一个点击位置（如果有的话）
        if (this.clickEvents.length > 0) {
            const lastPoint = this.clickEvents[0]; // 最新的点击点位
            const successDistance = 3;

            if (Math.abs(lastPoint.x - targetPoint.x) < successDistance && Math.abs(lastPoint.y - targetPoint.y) < successDistance) {
                await this.calibrationSuccess();
                this.stopCalibration();
                return;
            }

            const distanceX = lastPoint.x - targetPoint.x;
            const distanceY = lastPoint.y - targetPoint.y;

            // 使用退避算法动态调整移动步长
            this.robotMoveSize.x = this.calculateSingleAxisBackoff(successDistance, distanceX);
            this.robotMoveSize.y = this.calculateSingleAxisBackoff(successDistance, distanceY);
        }
        await this.calibrationRun();
    }

    /**
     * 校准成功处理
     */
    async calibrationSuccess() {
        await uni.showToast({title: '命中'});

        if (this.onCalibrationSuccess) {
            this.onCalibrationSuccess(this.robotPos);
        }
    }

    /**
     * 停止校准
     */
    stopCalibration() {
        this.isCalibrating = false;
        if (this.onCalibrationStop) {
            this.onCalibrationStop();
        }
    }

    /**
     * 获取当前校准状态
     */
    getCalibrationStatus() {
        return {
            isCalibrating: this.isCalibrating,
            robotPos: {...this.robotPos},
            robotMoveSize: {...this.robotMoveSize}
        };
    }

    /**
     * 重置校准状态
     */
    reset() {
        this.isCalibrating = false;
        this.robotPos = {x: 0, y: 0};
        this.robotMoveSize = {x: 10, y: 10};
        this.selectedPoint = null;
        this.deviceId = null;
        this.websocketService = null;
        this.clickEvents = null;
        this.onCalibrationSuccess = null;
        this.onCalibrationStop = null;
    }
}

// 导出单例实例
export const calibrationService = new CalibrationService();