/**
 * 记录所有设备当前的任务状态
 */
import {ConfigService} from "@/services/ConfigService";
import {mobileLog} from "@/services/RobotArmService";
import {TaskRunner} from "@/services/TaskRunner";


let _taskStatus = false;
let _taskRunner = null; // 保存 TaskRunner 实例

export class TaskStatusSocket {


    static getStatus() {
        return _taskStatus;
    }

    static getTaskRunner() {
        return _taskRunner;
    }

    static setupEventListeners(websocketService) {
        TaskStatusSocket.onGetTaskStatus(websocketService)
        TaskStatusSocket.onStopTask(websocketService)
        TaskStatusSocket.onStartTask(websocketService, () => {
            mobileLog('收到开始任务事件', 'info');

            // 如果已有运行中的任务，先停止
            if (_taskRunner && _taskRunner.isTaskRunning) {
                mobileLog('停止现有任务...', 'warning');
                _taskRunner.stopTask();
            }

            // 创建新的 TaskRunner 实例
            _taskRunner = new TaskRunner();
            _taskRunner.startTask();
        })
    }


    /**
     * 获取设备当前的任务状态
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static getTaskStatus(websocketService, deviceId, callback) {
        websocketService.on('get-task-status-return', (res) => {
            const status = res.json.status;
            callback(deviceId, status);
        });


        websocketService.send('get-task-status', {
            deviceId: deviceId,
        });
    }


    static onGetTaskStatus(websocketService) {
        websocketService.on('get-task-status', (res) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                websocketService.send('get-task-status-return', {
                    status: _taskStatus,
                });
            }
        })
    }

    /**
     * 通知任务开始
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static notifyStartTask(websocketService, deviceId, callback) {

        websocketService.on('notify-task-start-return', (res) => {
            if (res.json.deviceId === deviceId) {
                callback && callback();
            }
        })

        websocketService.send('notify-task-start', {
            deviceId: deviceId,
        });
    }


    /**
     * 监听任务开始
     * @param websocketService
     * @param callback
     */
    static onStartTask(websocketService, callback) {
        websocketService.on('notify-task-start', (res) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                _taskStatus = true;

                websocketService.send('notify-task-start-return', {
                    deviceId: deviceId,
                });

                callback();
            }
        });
    }


    /**
     * 通知任务停止
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static notifyStopTask(websocketService, deviceId, callback) {

        websocketService.on('notify-task-stop-return', (res) => {
            if (res.json.deviceId === deviceId) {
                callback && callback();
            }
        })

        websocketService.send('notify-task-stop', {
            deviceId: deviceId,
        });
    }

    /**
     * 监听任务停止
     * @param websocketService
     */
    static onStopTask(websocketService) {
        websocketService.on('notify-task-stop', (res) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                _taskStatus = false;

                // 停止 TaskRunner
                if (_taskRunner) {
                    mobileLog('收到停止任务事件，正在停止任务...', 'info');
                    _taskRunner.stopTask();
                    _taskRunner = null;
                }

                websocketService.send('notify-task-stop-return', {
                    deviceId: deviceId,
                });

            }
        });
    }


}