/**
 * 打开端口相关服务
 */
import {ConfigService} from "@/services/ConfigService";
import {mobileLog, RobotArmService} from "@/services/RobotArmService";


let _robotArmService = null;

export class RobotArmSocket {


    static getRobotArmService() {
        if (!_robotArmService) {
            uni.showModal({
                title: '请先打开端口',
                showCancel: false,
            }).then(_ => {
            })
            return;
        }
        return _robotArmService;
    }

    static setupEventListeners(websocketService) {
        RobotArmSocket.onOpenHandle(websocketService)
        RobotArmSocket.onCloseHandle(websocketService)
        RobotArmSocket.onGetStatus(websocketService)
        RobotArmSocket.onReset(websocketService)
        RobotArmSocket.onMoveTo(websocketService)
        RobotArmSocket.onMoveToByPx(websocketService)
        RobotArmSocket.onPress(websocketService)
        RobotArmSocket.onRelease(websocketService)
        RobotArmSocket.onClick(websocketService)
        RobotArmSocket.onSwipe(websocketService)
    }

    /**
     * 获取机械臂打开状态
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static getStatus(websocketService, deviceId, callback) {
        websocketService.on('get-robot-arm-return', (res) => {
            callback({
                handle: res.json.handle,
                port: res.json.port,
                deviceId,
            });
        });

        websocketService.send('get-robot-arm', {
            deviceId: deviceId,
        });
    }

    static onGetStatus(websocketService) {
        websocketService.on('get-robot-arm', (res) => {
            const deviceId = res.json.deviceId;
            mobileLog('收到查询 机械臂是否打开事件')
            if (deviceId === ConfigService.getDeviceId()) {
                websocketService.send('get-robot-arm-return', {
                    handle: _robotArmService ? _robotArmService.handle : 0,
                    port: _robotArmService ? _robotArmService.port : '',
                });
            }
        })
    }


    /**
     * 通知机械臂打开
     * @param websocketService
     * @param deviceId
     * @param port
     * @param callback
     */
    static notifyOpenHandle(websocketService, deviceId, port, callback) {
        websocketService.on('notify-robot-arm-open-return', res => {
            console.log('收到事件：notify-robot-arm-open-return');
            console.log(res);
            if (deviceId === res.json.deviceId) {
                callback(res);
            }
        });

        websocketService.send('notify-robot-arm-open', {
            deviceId: deviceId, port: port,
        });
    }


    /**
     * 监听机械臂打开
     * @param websocketService
     */
    static onOpenHandle(websocketService) {
        websocketService.on('notify-robot-arm-open', async (res) => {
            const deviceId = res.json.deviceId;
            const port = res.json.port;
            if (deviceId === ConfigService.getDeviceId()) {
                _robotArmService = await RobotArmService.openHandle(port)
                mobileLog('发出事件 notify-robot-arm-open-return')
                websocketService.send('notify-robot-arm-open-return', {
                    deviceId: deviceId,
                    port: port,
                    handle: _robotArmService.handle,
                });

            }
        });
    }


    /**
     * 通知机械臂关闭
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static notifyCloseHandle(websocketService, deviceId, callback) {

        websocketService.on('notify-robot-arm-stop-return', res => {
            if (deviceId === res.json.deviceId) {
                callback(res);
            }
        });

        websocketService.send('notify-robot-arm-stop', {
            deviceId: deviceId,
        });
    }

    /**
     * 监听械臂关闭
     * @param websocketService
     */
    static onCloseHandle(websocketService) {
        websocketService.on('notify-robot-arm-stop', async (res) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                await _robotArmService.closeHandle()
                _robotArmService = null;

                websocketService.send('notify-robot-arm-stop-return', {
                    deviceId: deviceId,
                });

            }
        });
    }

    // ==================== 移动端监听方法 ====================

    /**
     * 监听机械臂重置
     * @param websocketService
     */
    static onReset(websocketService) {
        websocketService.on('notify-robot-arm-reset', async (res) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog('收到机械臂重置事件');
                const result = await _robotArmService.reset();
                websocketService.send('notify-robot-arm-reset-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂移动到坐标
     * @param websocketService
     */
    static onMoveTo(websocketService) {
        websocketService.on('notify-robot-arm-moveto', async (res) => {
            const deviceId = res.json.deviceId;
            const x = res.json.x;
            const y = res.json.y;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂移动事件: x=${x}, y=${y}`);
                const result = await _robotArmService.moveTo(x, y);
                websocketService.send('notify-robot-arm-moveto-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂通过像素移动
     * @param websocketService
     */
    static onMoveToByPx(websocketService) {
        websocketService.on('notify-robot-arm-movetobypx', async (res) => {
            const deviceId = res.json.deviceId;
            const x = res.json.x;
            const y = res.json.y;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂像素移动事件: x=${x}, y=${y}`);
                const result = await _robotArmService.moveToByPx(x, y);
                websocketService.send('notify-robot-arm-movetobypx-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂按下
     * @param websocketService
     */
    static onPress(websocketService) {
        websocketService.on('notify-robot-arm-press', async (res) => {
            const deviceId = res.json.deviceId;
            const z = res.json.z || 8;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂按下事件: z=${z}`);
                const result = await _robotArmService.press(z);
                websocketService.send('notify-robot-arm-press-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂抬起
     * @param websocketService
     */
    static onRelease(websocketService) {
        websocketService.on('notify-robot-arm-release', async (res) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog('收到机械臂抬起事件');
                const result = await _robotArmService.release();
                websocketService.send('notify-robot-arm-release-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂点击
     * @param websocketService
     */
    static onClick(websocketService) {
        websocketService.on('notify-robot-arm-click', async (res) => {
            const deviceId = res.json.deviceId;
            const x = res.json.x;
            const y = res.json.y;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂点击事件: x=${x}, y=${y}`);
                const result = await _robotArmService.click(x, y);
                websocketService.send('notify-robot-arm-click-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂滑动
     * @param websocketService
     */
    static onSwipe(websocketService) {
        websocketService.on('notify-robot-arm-swipe', async (res) => {
            const deviceId = res.json.deviceId;
            const x1 = res.json.x1;
            const y1 = res.json.y1;
            const x2 = res.json.x2;
            const y2 = res.json.y2;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂滑动事件: from(${x1},${y1}) to(${x2},${y2})`);
                const result = await _robotArmService.swipe(x1, y1, x2, y2);
                websocketService.send('notify-robot-arm-swipe-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    // ==================== PC端发送事件执行方法 ====================

    /**
     * 通知机械臂重置
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static notifyReset(websocketService, deviceId, callback) {
        websocketService.on('notify-robot-arm-reset-return', res => {
            if (deviceId === res.json.deviceId) {
                callback && callback(res.json.status);
            }
        });

        websocketService.send('notify-robot-arm-reset', {
            deviceId: deviceId,
        });
    }

    /**
     * 通知机械臂移动到指定坐标
     * @param websocketService
     * @param deviceId
     * @param x
     * @param y
     * @param callback
     */
    static notifyMoveTo(websocketService, deviceId, x, y, callback) {
        websocketService.on('notify-robot-arm-moveto-return', res => {
            if (deviceId === res.json.deviceId) {
                callback && callback(res.json.status);
            }
        });

        websocketService.send('notify-robot-arm-moveto', {
            deviceId: deviceId,
            x: x,
            y: y,
        });
    }

    /**
     * 通知机械臂通过像素坐标移动
     * @param websocketService
     * @param deviceId
     * @param x
     * @param y
     * @param callback
     */
    static notifyMoveToByPx(websocketService, deviceId, x, y, callback) {
        websocketService.on('notify-robot-arm-movetobypx-return', res => {
            if (deviceId === res.json.deviceId) {
                callback && callback(res.json.status);
            }
        });

        websocketService.send('notify-robot-arm-movetobypx', {
            deviceId: deviceId,
            x: x,
            y: y,
        });
    }

    /**
     * 通知机械臂按下（触控笔下降）
     * @param websocketService
     * @param deviceId
     * @param z
     * @param callback
     */
    static notifyPress(websocketService, deviceId, z = 8, callback) {
        websocketService.on('notify-robot-arm-press-return', res => {
            if (deviceId === res.json.deviceId) {
                callback && callback(res.json.status);
            }
        });

        websocketService.send('notify-robot-arm-press', {
            deviceId: deviceId,
            z: z,
        });
    }

    /**
     * 通知机械臂抬起（触控笔抬起）
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static notifyRelease(websocketService, deviceId, callback) {
        websocketService.on('notify-robot-arm-release-return', res => {
            if (deviceId === res.json.deviceId) {
                callback && callback(res.json.status);
            }
        });

        websocketService.send('notify-robot-arm-release', {
            deviceId: deviceId,
        });
    }

    /**
     * 通知机械臂点击（移动到坐标后按下再抬起）
     * @param websocketService
     * @param deviceId
     * @param x
     * @param y
     * @param callback
     */
    static notifyClick(websocketService, deviceId, x, y, callback) {
        websocketService.on('notify-robot-arm-click-return', res => {
            if (deviceId === res.json.deviceId) {
                callback && callback(res.json.status);
            }
        });

        websocketService.send('notify-robot-arm-click', {
            deviceId: deviceId,
            x: x,
            y: y,
        });
    }

    /**
     * 通知机械臂滑动（从起点滑到终点）
     * @param websocketService
     * @param deviceId
     * @param x1
     * @param y1
     * @param x2
     * @param y2
     * @param callback
     */
    static notifySwipe(websocketService, deviceId, x1, y1, x2, y2, callback) {
        websocketService.on('notify-robot-arm-swipe-return', res => {
            if (deviceId === res.json.deviceId) {
                callback && callback(res.json.status);
            }
        });

        websocketService.send('notify-robot-arm-swipe', {
            deviceId: deviceId,
            x1: x1,
            y1: y1,
            x2: x2,
            y2: y2,
        });
    }


}