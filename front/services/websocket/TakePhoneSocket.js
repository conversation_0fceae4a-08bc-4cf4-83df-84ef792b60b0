/**
 * 机械臂服务类
 * 提供机械臂控制的静态方法
 */
export class TakePhoneSocket {


    /**
     * 手机发送照片到PC
     * @param websocketService
     * @param photoData
     * @param deviceId
     */
    static sendPhoneToPC(websocketService, photoData, deviceId) {
        websocketService.send('take-photo-result', {
            deviceId: deviceId,
        }, photoData);
    }

    /**
     * PC 接收照片
     * @param websocketService
     * @param callback
     */
    static receivePhoneByMobile(websocketService, callback) {
        websocketService.on('take-photo-result', (message) => {
            callback(message.blob, message.json.deviceId)
        });
    }


    /**
     * 通知手机拍照
     * @param websocketService
     * @param mobileId
     */
    static sendTakePhone(websocketService, mobileId) {
        websocketService.send('take-photo', {
            deviceId: mobileId
        });
    }

    /**
     * 手机监听拍照指令
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static onTakePhone(websocketService, deviceId, callback) {
        websocketService.on('take-photo', (res) => {
            if (res.json.deviceId === deviceId) {
                callback();
            }
        });

    }


}