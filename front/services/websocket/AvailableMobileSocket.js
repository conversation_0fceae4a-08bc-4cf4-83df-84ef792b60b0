export class AvailableMobileSocket {


    /**
     * 监听获取在线设备
     * @param websocketService
     * @param deviceId
     */
    static onGetAvailableMobiles(websocketService, deviceId) {
        websocketService.on('get_available_mobiles', (message) => {
            console.log('收到获取在线设备请求:', message);
            websocketService.send('available_mobile', {deviceId: deviceId});
        });

    }


    /**
     * 发送请求，获取在线设备
     * @param websocketService
     */
    static getAvailableMobiles(websocketService) {
        websocketService.send('get_available_mobiles');
    }

    /**
     * 收到了设备在线的通知
     * @param websocketService
     * @param callback
     */
    static onAvailableMobile(websocketService, callback) {
        websocketService.on('available_mobile', (message) => {
            console.log('收到可用手机列表:', message);
            callback(message.json.deviceId);
        });
    }

}