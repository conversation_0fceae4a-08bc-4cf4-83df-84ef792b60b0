import {ConfigService} from "@/services/ConfigService";
import {ImageRecognitionService} from "@/services/ImageRecognitionService";
import {mobileLog, sleep} from "@/services/RobotArmService";
import {CountService} from "@/services/CountService";
import {TaskStatusSocket} from "@/services/websocket/TaskStatusSocket";
import {RobotArmSocket} from "@/services/websocket/RobotArmSocket";

export class TaskRunner {
    constructor() {

        this.xIncrement = 0; // X轴增量
        this.yFirstRow = 0; // 第一行Y轴增量
        this.ySecondRow = 0; // 第二行Y轴增量

        this.maxMoveX = 0; // 最大X轴移动距离

        this.currX = 0; // 当前X轴位置
        this.currY = 0; // 当前Y轴位置

        this.direction = 1; // 移动方向：1为向右，-1为向左
        this.currentRow = 1; // 当前行：1为第一行，2为第二行

        this.robotArmService = RobotArmSocket.getRobotArmService();
        this.imageRecognitionService = new ImageRecognitionService();

        // 点击队列配置
        this.clickQueue = [];
        this.isProcessingClick = false;
        this.maxQueueSize = 50; // 队列最大大小，防止内存泄漏
        this.queueCleanupThreshold = 40; // 队列清理阈值

        // 内存管理
        this.memoryCleanupInterval = null;
        this.memoryCleanupIntervalMs = 30000; // 30秒清理一次内存
        this.lastMemoryCleanup = Date.now();

        // 任务状态管理
        this.isTaskRunning = false;
        this.taskStartTime = null;

        // 性能监控
        this.performanceStats = {
            totalProcessed: 0,
            totalErrors: 0,
            avgProcessingTime: 0,
            lastProcessingTime: 0
        };

        // 定时器管理
        this.timers = new Map(); // 存储所有定时器
        this.intervals = new Map(); // 存储所有间隔定时器

        // 启动内存清理定时器
        this.startMemoryCleanup();

        // 启动队列清理定时器
        this.startQueueCleanup();

        // 健康监控
        this.healthMonitor = {
            lastHealthCheck: Date.now(),
            healthCheckInterval: 30000, // 30秒检查一次
            consecutiveErrors: 0,
            maxConsecutiveErrors: 5,
            systemHealth: 'good', // good, warning, critical
            alerts: []
        };

        // 启动健康监控
        this.startHealthMonitoring();
    }

    /**
     * 创建并管理 setTimeout
     */
    createTimeout(callback, delay, name = null) {
        const timerId = setTimeout(() => {
            try {
                callback();
            } catch (error) {
                mobileLog(`定时器回调执行出错 [${name || 'unnamed'}]: ${error.message}`, 'error');
            } finally {
                // 执行完成后从管理器中移除
                this.timers.delete(timerId);
            }
        }, delay);

        // 添加到管理器
        this.timers.set(timerId, {
            name: name || `timeout_${Date.now()}`,
            type: 'timeout',
            createdAt: Date.now(),
            delay: delay
        });

        return timerId;
    }

    /**
     * 创建并管理 setInterval
     */
    createInterval(callback, interval, name = null) {
        const intervalId = setInterval(() => {
            try {
                callback();
            } catch (error) {
                mobileLog(`间隔定时器回调执行出错 [${name || 'unnamed'}]: ${error.message}`, 'error');
            }
        }, interval);

        // 添加到管理器
        this.intervals.set(intervalId, {
            name: name || `interval_${Date.now()}`,
            type: 'interval',
            createdAt: Date.now(),
            interval: interval
        });

        return intervalId;
    }

    /**
     * 清除指定的定时器
     */
    clearManagedTimeout(timerId) {
        if (this.timers.has(timerId)) {
            clearTimeout(timerId);
            this.timers.delete(timerId);
        }
    }

    /**
     * 清除指定的间隔定时器
     */
    clearManagedInterval(intervalId) {
        if (this.intervals.has(intervalId)) {
            clearInterval(intervalId);
            this.intervals.delete(intervalId);
        }
    }

    /**
     * 清除所有定时器
     */
    clearAllTimers() {
        // 清除所有 setTimeout
        this.timers.forEach((info, timerId) => {
            clearTimeout(timerId);
        });
        this.timers.clear();

        // 清除所有 setInterval
        this.intervals.forEach((info, intervalId) => {
            clearInterval(intervalId);
        });
        this.intervals.clear();

        mobileLog('所有定时器已清除', 'info');
    }

    /**
     * 获取定时器统计信息
     */
    getTimerStats() {
        return {
            timeouts: {
                count: this.timers.size,
                list: Array.from(this.timers.values())
            },
            intervals: {
                count: this.intervals.size,
                list: Array.from(this.intervals.values())
            }
        };
    }


    startTask() {
        if (this.isTaskRunning) {
            mobileLog('任务已在运行中，忽略重复启动', 'warning');
            return;
        }

        this.xIncrement = ConfigService.getXIncrement();
        this.yFirstRow = ConfigService.getYFirstRow();
        this.ySecondRow = ConfigService.getYSecondRow();
        this.maxMoveX = ConfigService.getMaxMoveX();
        // 最后一屏拍照位置了
        this.maxMoveX -= (1280 / CountService.getPxWidth() - 10);

        // 初始化位置和状态
        this.currX = 0;
        this.currY = this.yFirstRow;
        this.direction = 1;
        this.currentRow = 1;
        this.isTaskRunning = true;
        this.taskStartTime = Date.now();

        // 重置性能统计
        this.performanceStats = {
            totalProcessed: 0,
            totalErrors: 0,
            avgProcessingTime: 0,
            lastProcessingTime: 0
        };

        // 清空队列
        this.clearClickQueue();

        mobileLog('任务开始执行', 'info');
        mobileLog(`配置参数: X增量=${this.xIncrement}, Y第一行=${this.yFirstRow}, Y第二行=${this.ySecondRow}`, 'info');

        this.runTask();
    }


    async runTask() {
        try {
            while (TaskStatusSocket.getStatus() && this.isTaskRunning) {
                const cycleStartTime = Date.now();

                try {
                    // 并行执行：处理点击队列的同时准备下一步操作
                    const queueProcessPromise = this.processClickQueue();

                    // 移动到指定位置
                    await this.moveToPosition();

                    // 等待队列处理完成（如果还没完成）
                    await queueProcessPromise;

                    // 开始异步拍照识别（不等待完成）
                    await this.captureAndStartRecognition();

                    // 在等待摄像头稳定的时间里，执行其他任务
                    await this.performBackgroundTasks();

                    // 计算下一个位置
                    this.countNextPos();

                    // 更新性能统计
                    const processingTime = Date.now() - cycleStartTime;
                    this.updatePerformanceStats(processingTime);

                    // 智能等待：根据系统负载调整等待时间
                    await this.smartWait();

                } catch (error) {
                    this.performanceStats.totalErrors++;
                    this.healthMonitor.consecutiveErrors++;
                    mobileLog(`任务循环出错: ${error.message}`, 'error');

                    // 智能错误恢复
                    await this.handleTaskError(error);
                }
            }
        } catch (error) {
            mobileLog(`任务运行出现严重错误: ${error.message}`, 'error');
        } finally {
            this.stopTask();
        }

        mobileLog('任务循环结束', 'info');
    }

    /**
     * 执行后台任务（在机械臂等待时间执行）
     */
    async performBackgroundTasks() {
        const tasks = [];

        // 内存清理
        if (this.shouldPerformMemoryCleanup()) {
            tasks.push(this.performMemoryCleanup());
        }

        // 队列优化
        if (this.clickQueue.length > 10) {
            tasks.push(Promise.resolve().then(() => this.optimizeQueueOrder()));
        }

        // 清理过期目标
        if (this.clickQueue.length > 20) {
            tasks.push(Promise.resolve().then(() => this.cleanupExpiredTargets()));
        }

        // 并行执行所有后台任务
        if (tasks.length > 0) {
            try {
                await Promise.allSettled(tasks);
                mobileLog(`执行了 ${tasks.length} 个后台任务`, 'info');
            } catch (error) {
                mobileLog(`后台任务执行出错: ${error.message}`, 'error');
            }
        }
    }

    /**
     * 智能等待：根据系统状态调整等待时间
     */
    async smartWait() {
        let waitTime = 100; // 基础等待时间

        // 根据队列长度调整
        if (this.clickQueue.length > 30) {
            waitTime = 50; // 队列很长时减少等待
        } else if (this.clickQueue.length < 5) {
            waitTime = 200; // 队列很短时增加等待，给识别更多时间
        }

        // 根据错误率调整
        const errorRate = this.performanceStats.totalProcessed > 0 ?
            this.performanceStats.totalErrors / this.performanceStats.totalProcessed : 0;
        if (errorRate > 0.1) {
            waitTime += 100; // 错误率高时增加等待时间
        }

        // 根据处理速度调整
        if (this.performanceStats.avgProcessingTime > 5000) {
            waitTime += 50; // 处理慢时稍微增加等待
        }

        if (waitTime > 100) {
            await sleep(waitTime - 100); // 减去基础等待时间
        }
    }

    /**
     * 处理任务错误
     */
    async handleTaskError(error) {
        const errorType = this.classifyError(error);

        switch (errorType) {
            case 'network':
                mobileLog('网络错误，等待网络恢复...', 'warning');
                await sleep(5000);
                break;

            case 'timeout':
                mobileLog('超时错误，减少并发操作...', 'warning');
                await sleep(2000);
                break;

            case 'resource':
                mobileLog('资源错误，执行清理操作...', 'warning');
                this.performMemoryCleanup();
                await sleep(3000);
                break;

            default:
                mobileLog('未知错误，标准恢复流程...', 'warning');
                await sleep(2000);
        }
    }

    /**
     * 错误分类
     */
    classifyError(error) {
        const message = error.message.toLowerCase();

        if (message.includes('network') || message.includes('连接') || message.includes('websocket')) {
            return 'network';
        }
        if (message.includes('timeout') || message.includes('超时')) {
            return 'timeout';
        }
        if (message.includes('memory') || message.includes('内存') || message.includes('resource')) {
            return 'resource';
        }

        return 'unknown';
    }

    /**
     * 检查是否应该执行内存清理
     */
    shouldPerformMemoryCleanup() {
        const timeSinceLastCleanup = Date.now() - this.lastMemoryCleanup;
        return timeSinceLastCleanup > this.memoryCleanupIntervalMs ||
               this.clickQueue.length > this.queueCleanupThreshold;
    }

    /**
     * 移动到指定位置
     */
    async moveToPosition() {
        mobileLog(`移动到位置: X=${this.currX}, Y=${this.currY}, 第${this.currentRow}行`, 'info');
        await this.robotArmService.moveTo(this.currX, this.currY);
    }

    /**
     * 等待摄像头稳定并开始拍照识别（异步）
     */
    async captureAndStartRecognition() {
        // 等待摄像头画面稳定
        await sleep(600);

        // 异步拍照并识别，不等待识别结果
        // 拍照完成后立即返回，不阻塞机械臂移动
        this.startAsyncRecognition();

        // 立即返回，让机械臂可以继续移动到下一个位置
        mobileLog('拍照已启动，机械臂继续移动', 'info');
    }

    /**
     * 异步开始识别过程（改进版）
     */
    startAsyncRecognition() {
        // 记录当前机械臂坐标，用于后续计算点击位置
        const recognitionX = this.currX;
        const recognitionY = this.currY;
        const recognitionId = `recognition_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        mobileLog(`识别过程已启动，记录识别位置: X=${recognitionX}, Y=${recognitionY}, ID=${recognitionId}`, 'info');

        // 使用超时控制的异步识别
        this.performAsyncRecognitionWithTimeout(recognitionX, recognitionY, recognitionId)
            .catch(error => {
                mobileLog(`识别过程失败 [${recognitionId}]: ${error.message}`, 'error');
                this.performanceStats.totalErrors++;
            });
    }

    /**
     * 执行带超时控制的异步识别
     */
    async performAsyncRecognitionWithTimeout(recognitionX, recognitionY, recognitionId, timeoutMs = 15000) {
        return new Promise((resolve, reject) => {
            let isCompleted = false;

            // 设置超时
            const timeoutHandle = setTimeout(() => {
                if (!isCompleted) {
                    isCompleted = true;
                    reject(new Error(`识别超时 [${recognitionId}]`));
                }
            }, timeoutMs);

            try {
                // 执行识别
                this.imageRecognitionService.captureAndRecognize((recognitionResult) => {
                    if (isCompleted) {
                        mobileLog(`识别结果已过期，忽略 [${recognitionId}]`, 'warning');
                        return;
                    }

                    isCompleted = true;
                    clearTimeout(timeoutHandle);

                    try {
                        this.processRecognitionResult(recognitionResult, recognitionX, recognitionY, recognitionId);
                        resolve(recognitionResult);
                    } catch (error) {
                        reject(error);
                    }
                });
            } catch (error) {
                if (!isCompleted) {
                    isCompleted = true;
                    clearTimeout(timeoutHandle);
                    reject(error);
                }
            }
        });
    }

    /**
     * 处理识别结果
     */
    processRecognitionResult(recognitionResult, recognitionX, recognitionY, recognitionId) {
        try {
            if (recognitionResult && recognitionResult.results) {
                mobileLog(`处理识别结果 [${recognitionId}]: 发现 ${recognitionResult.results.length} 个目标`, 'info');

                for (let i = 0; i < recognitionResult.results.length; i++) {
                    // 将识别时的机械臂坐标附加到目标对象上
                    const target = recognitionResult.results[i];
                    target.recognitionX = recognitionX;
                    target.recognitionY = recognitionY;
                    target.recognitionId = recognitionId;
                    target.recognitionTime = Date.now();

                    this.addToClickQueue(target);
                }
            } else {
                mobileLog(`识别结果为空 [${recognitionId}]`, 'info');
            }
        } catch (error) {
            mobileLog(`处理识别结果出错 [${recognitionId}]: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 添加目标到点击队列
     * @param {Object} target 要点击的目标对象
     */
    addToClickQueue(target) {
        try {
            // 检查队列大小限制
            if (this.clickQueue.length >= this.maxQueueSize) {
                mobileLog(`队列已满(${this.maxQueueSize})，移除最旧的目标`, 'warning');
                this.clickQueue.shift(); // 移除最旧的目标
            }

            const bbox = target.bbox;
            const centerX = ((bbox[0] + bbox[2]) / 2).toFixed(2);
            const centerY = ((bbox[1] + bbox[3]) / 2).toFixed(2);

            mobileLog(`识别结果中心位置(像素坐标): (${centerX}, ${centerY})`, 'info');

            // 转换为机械臂坐标系
            const retX = (1280 - centerY) / CountService.getPxWidth();
            const retY = centerX / CountService.getPxWidth();

            // 获取识别时的机械臂位置
            const recognitionX = target.recognitionX;
            const recognitionY = target.recognitionY;

            // 计算真正的点击坐标：识别位置 + 识别结果的相对坐标
            const finalX = recognitionX + retX + ConfigService.getOffsetX();
            const finalY = recognitionY + retY + ConfigService.getOffsetY();

            mobileLog(`识别位置: (${recognitionX}, ${recognitionY}), 相对坐标: (${retX}, ${retY})`, 'info');
            mobileLog(`最终点击坐标: (${finalX}, ${finalY})`, 'info');

            // 改进的重复目标检测
            const isDuplicate = this.isDuplicateTarget(target, finalX, finalY);

            if (!isDuplicate) {
                // 将计算好的坐标添加到目标对象中
                target.finalX = finalX;
                target.finalY = finalY;
                target.addedTime = Date.now(); // 添加时间戳
                target.priority = this.calculateTargetPriority(target, finalX, finalY); // 计算优先级

                // 按优先级插入队列
                this.insertTargetByPriority(target);

                mobileLog(`目标已加入点击队列: ${target.class_name}, 坐标: (${finalX}, ${finalY}), 优先级: ${target.priority}, 当前队列长度: ${this.clickQueue.length}`, 'info');
            }
        } catch (error) {
            mobileLog(`添加目标到点击队列失败: ${error.message}`, 'error');
        }
    }

    /**
     * 检查是否为重复目标（改进的算法）
     */
    isDuplicateTarget(target, finalX, finalY) {
        const threshold = 5; // 距离阈值
        const timeThreshold = 10000; // 10秒内的目标认为可能重复
        const currentTime = Date.now();

        return this.clickQueue.some(existingTarget => {
            // 检查类型和时间
            if (existingTarget.class_name === target.class_name) {
                const timeDiff = currentTime - (existingTarget.addedTime || 0);

                // 如果是很久之前的目标，不认为是重复
                if (timeDiff > timeThreshold) {
                    return false;
                }

                // 计算距离
                const distance = Math.sqrt(
                    Math.pow(existingTarget.finalX - finalX, 2) +
                    Math.pow(existingTarget.finalY - finalY, 2)
                );

                if (distance < threshold) {
                    mobileLog(`检测到重复点击目标: ${target.class_name}, 距离: ${distance.toFixed(2)}, 时间差: ${timeDiff}ms, 跳过添加`, 'warning');
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * 计算目标优先级
     */
    calculateTargetPriority(target, finalX, finalY) {
        let priority = 0;

        // 基于置信度的优先级
        priority += (target.confidence || 0) * 100;

        // 基于距离当前位置的优先级（距离越近优先级越高）
        const distance = Math.sqrt(
            Math.pow(this.currX - finalX, 2) +
            Math.pow(this.currY - finalY, 2)
        );
        priority += Math.max(0, 100 - distance);

        // 基于目标类型的优先级（可以根据需要调整）
        const typePriority = {
            'high_priority': 50,
            'medium_priority': 25,
            'low_priority': 0
        };
        priority += typePriority[target.class_name] || 10;

        return Math.round(priority);
    }

    /**
     * 按优先级插入目标到队列
     */
    insertTargetByPriority(target) {
        // 找到合适的插入位置（优先级从高到低）
        let insertIndex = this.clickQueue.length;
        for (let i = 0; i < this.clickQueue.length; i++) {
            if (target.priority > (this.clickQueue[i].priority || 0)) {
                insertIndex = i;
                break;
            }
        }

        this.clickQueue.splice(insertIndex, 0, target);
    }

    /**
     * 处理点击队列中的所有任务（改进版）
     */
    async processClickQueue() {
        if (this.clickQueue.length === 0 || this.isProcessingClick) {
            return;
        }

        this.isProcessingClick = true;
        const queueLength = this.clickQueue.length;
        mobileLog(`开始处理点击队列，共${queueLength}个目标`, 'info');

        let processedCount = 0;
        let errorCount = 0;

        try {
            while (this.clickQueue.length > 0 && this.isTaskRunning) {
                const target = this.clickQueue.shift();

                try {
                    await this.handleTargetClickWithTimeout(target);
                    processedCount++;
                } catch (error) {
                    errorCount++;
                    mobileLog(`处理目标失败: ${target.class_name}, 错误: ${error.message}`, 'error');

                    // 如果是超时错误，可能需要跳过当前目标继续处理
                    if (error.message.includes('超时')) {
                        continue;
                    }

                    // 其他错误，等待一小段时间后继续
                    await sleep(500);
                }
            }

            mobileLog(`点击队列处理完成: 成功${processedCount}个, 失败${errorCount}个`, 'success');
        } catch (error) {
            mobileLog(`处理点击队列出现严重错误: ${error.message}`, 'error');
        } finally {
            this.isProcessingClick = false;
        }
    }

    /**
     * 带超时控制的目标点击处理
     */
    async handleTargetClickWithTimeout(target, timeoutMs = 10000) {
        return new Promise(async (resolve, reject) => {
            const timeoutHandle = setTimeout(() => {
                reject(new Error(`点击目标超时: ${target.class_name}`));
            }, timeoutMs);

            try {
                await this.handleTargetClick(target);
                clearTimeout(timeoutHandle);
                resolve();
            } catch (error) {
                clearTimeout(timeoutHandle);
                reject(error);
            }
        });
    }

    /**
     * 处理目标点击逻辑（改进版）
     * @param {Object} target 要点击的目标对象
     */
    async handleTargetClick(target) {
        const startTime = Date.now();

        try {
            // 验证目标数据
            if (!target || !target.finalX || !target.finalY) {
                throw new Error('目标数据无效');
            }

            // 检查目标是否过期（超过30秒的识别结果可能已经不准确）
            if (target.recognitionTime && Date.now() - target.recognitionTime > 30000) {
                mobileLog(`目标已过期，跳过点击: ${target.class_name}`, 'warning');
                return;
            }

            // 检查机械臂服务是否可用
            if (!this.robotArmService) {
                throw new Error('机械臂服务不可用');
            }

            mobileLog(`开始点击目标: ${target.class_name}, 坐标: (${target.finalX}, ${target.finalY})`, 'info');

            // 执行点击操作
            await this.robotArmService.click(target.finalX, target.finalY);

            const duration = Date.now() - startTime;
            mobileLog(`点击目标完成: ${target.class_name}, 坐标: (${target.finalX}, ${target.finalY}), 耗时: ${duration}ms`, 'success');

        } catch (error) {
            const duration = Date.now() - startTime;
            mobileLog(`点击目标失败: ${target.class_name || '未知'}, 耗时: ${duration}ms, 错误: ${error.message}`, 'error');
            throw error;
        }
    }

    countNextPos() {
        // 根据方向移动X轴
        this.currX += this.xIncrement * this.direction;

        // 检查是否到达边界
        if (this.direction === 1 && this.currX >= this.maxMoveX) {
            // 向右移动到达最大位置，切换到下一行
            this.currX = this.maxMoveX;
            mobileLog('到达右边界，准备切换到下一行', 'info');
            this.switchToNextRow();
        } else if (this.direction === -1 && this.currX <= 0) {
            // 向左移动到达最小位置，切换到下一行
            this.currX = 0;
            mobileLog('到达左边界，准备切换到下一行', 'info');
            this.switchToNextRow();
        }
    }

    switchToNextRow() {
        if (this.currentRow === 1) {
            // 从第一行切换到第二行
            this.currentRow = 2;
            this.currY = this.ySecondRow;
            this.direction = -1; // 第二行从右到左
            mobileLog('切换到第二行，方向：从右到左', 'info');
        } else {
            // 从第二行切换到第一行
            this.currentRow = 1;
            this.currY = this.yFirstRow;
            this.direction = 1; // 第一行从左到右
            mobileLog('切换到第一行，方向：从左到右', 'info');
        }
    }

    /**
     * 停止任务并清理资源
     */
    stopTask() {
        this.isTaskRunning = false;
        this.clearClickQueue();
        this.stopMemoryCleanup();
        this.stopQueueCleanup();
        this.stopHealthMonitoring();
        this.clearAllTimers(); // 清理所有定时器

        const runTime = this.taskStartTime ? Date.now() - this.taskStartTime : 0;
        mobileLog(`任务已停止，运行时间: ${Math.round(runTime / 1000)}秒`, 'info');
        mobileLog(`性能统计 - 处理总数: ${this.performanceStats.totalProcessed}, 错误总数: ${this.performanceStats.totalErrors}`, 'info');

        // 输出最终健康报告
        this.reportHealthStatus();

        // 输出定时器统计
        const timerStats = this.getTimerStats();
        if (timerStats.timeouts.count > 0 || timerStats.intervals.count > 0) {
            mobileLog(`清理定时器: ${timerStats.timeouts.count} 个 timeout, ${timerStats.intervals.count} 个 interval`, 'info');
        }
    }

    /**
     * 启动内存清理定时器
     */
    startMemoryCleanup() {
        this.stopMemoryCleanup(); // 先停止现有的

        this.memoryCleanupInterval = this.createInterval(() => {
            this.performMemoryCleanup();
        }, this.memoryCleanupIntervalMs, 'memory_cleanup');
    }

    /**
     * 停止内存清理定时器
     */
    stopMemoryCleanup() {
        if (this.memoryCleanupInterval) {
            this.clearManagedInterval(this.memoryCleanupInterval);
            this.memoryCleanupInterval = null;
        }
    }

    /**
     * 启动队列清理定时器
     */
    startQueueCleanup() {
        this.stopQueueCleanup(); // 先停止现有的

        // 每60秒清理一次过期目标
        this.queueCleanupInterval = this.createInterval(() => {
            this.cleanupExpiredTargets();
            this.optimizeQueueOrder();
        }, 60000, 'queue_cleanup');
    }

    /**
     * 停止队列清理定时器
     */
    stopQueueCleanup() {
        if (this.queueCleanupInterval) {
            this.clearManagedInterval(this.queueCleanupInterval);
            this.queueCleanupInterval = null;
        }
    }

    /**
     * 执行内存清理
     */
    performMemoryCleanup() {
        try {
            // 清理过大的队列
            if (this.clickQueue.length > this.queueCleanupThreshold) {
                const removed = this.clickQueue.splice(0, this.clickQueue.length - this.queueCleanupThreshold);
                mobileLog(`内存清理: 移除了 ${removed.length} 个过期队列项`, 'info');
            }

            // 强制垃圾回收（如果可用）
            if (typeof window !== 'undefined' && window.gc) {
                window.gc();
            }

            this.lastMemoryCleanup = Date.now();
            mobileLog('内存清理完成', 'info');
        } catch (error) {
            mobileLog(`内存清理失败: ${error.message}`, 'error');
        }
    }

    /**
     * 如果需要则执行内存清理
     */
    async performMemoryCleanupIfNeeded() {
        const timeSinceLastCleanup = Date.now() - this.lastMemoryCleanup;
        if (timeSinceLastCleanup > this.memoryCleanupIntervalMs) {
            this.performMemoryCleanup();
        }
    }

    /**
     * 更新性能统计
     */
    updatePerformanceStats(processingTime) {
        this.performanceStats.totalProcessed++;
        this.performanceStats.lastProcessingTime = processingTime;

        // 计算平均处理时间
        if (this.performanceStats.totalProcessed === 1) {
            this.performanceStats.avgProcessingTime = processingTime;
        } else {
            this.performanceStats.avgProcessingTime =
                (this.performanceStats.avgProcessingTime * (this.performanceStats.totalProcessed - 1) + processingTime)
                / this.performanceStats.totalProcessed;
        }
    }

    /**
     * 清空点击队列
     */
    clearClickQueue() {
        const clearedCount = this.clickQueue.length;
        this.clickQueue.length = 0; // 高效清空数组
        if (clearedCount > 0) {
            mobileLog(`点击队列已清空，移除了 ${clearedCount} 个目标`, 'info');
        }
    }

    /**
     * 获取队列统计信息
     */
    getQueueStats() {
        const stats = {
            totalCount: this.clickQueue.length,
            byType: {},
            avgPriority: 0,
            oldestTarget: null,
            newestTarget: null
        };

        if (this.clickQueue.length > 0) {
            let totalPriority = 0;
            let oldestTime = Date.now();
            let newestTime = 0;

            this.clickQueue.forEach(target => {
                // 按类型统计
                stats.byType[target.class_name] = (stats.byType[target.class_name] || 0) + 1;

                // 优先级统计
                totalPriority += target.priority || 0;

                // 时间统计
                const targetTime = target.addedTime || target.recognitionTime || 0;
                if (targetTime < oldestTime) {
                    oldestTime = targetTime;
                    stats.oldestTarget = target;
                }
                if (targetTime > newestTime) {
                    newestTime = targetTime;
                    stats.newestTarget = target;
                }
            });

            stats.avgPriority = totalPriority / this.clickQueue.length;
        }

        return stats;
    }

    /**
     * 清理过期目标
     */
    cleanupExpiredTargets(maxAgeMs = 60000) { // 默认60秒过期
        const currentTime = Date.now();
        const initialLength = this.clickQueue.length;

        this.clickQueue = this.clickQueue.filter(target => {
            const targetTime = target.addedTime || target.recognitionTime || 0;
            const age = currentTime - targetTime;

            if (age > maxAgeMs) {
                mobileLog(`移除过期目标: ${target.class_name}, 年龄: ${Math.round(age/1000)}秒`, 'info');
                return false;
            }
            return true;
        });

        const removedCount = initialLength - this.clickQueue.length;
        if (removedCount > 0) {
            mobileLog(`清理完成，移除了 ${removedCount} 个过期目标`, 'info');
        }

        return removedCount;
    }

    /**
     * 优化队列排序（重新排序现有队列）
     */
    optimizeQueueOrder() {
        if (this.clickQueue.length <= 1) {
            return;
        }

        // 重新计算所有目标的优先级
        this.clickQueue.forEach(target => {
            target.priority = this.calculateTargetPriority(target, target.finalX, target.finalY);
        });

        // 重新排序
        this.clickQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));

        mobileLog(`队列已重新排序，共 ${this.clickQueue.length} 个目标`, 'info');
    }

    /**
     * 启动健康监控
     */
    startHealthMonitoring() {
        this.stopHealthMonitoring(); // 先停止现有的

        this.healthMonitorInterval = this.createInterval(() => {
            this.performHealthCheck();
        }, this.healthMonitor.healthCheckInterval, 'health_monitor');
    }

    /**
     * 停止健康监控
     */
    stopHealthMonitoring() {
        if (this.healthMonitorInterval) {
            this.clearManagedInterval(this.healthMonitorInterval);
            this.healthMonitorInterval = null;
        }
    }

    /**
     * 执行健康检查
     */
    performHealthCheck() {
        try {
            const currentTime = Date.now();
            const timeSinceLastCheck = currentTime - this.healthMonitor.lastHealthCheck;

            // 检查各项健康指标
            const healthStatus = this.checkSystemHealth();

            // 更新健康状态
            this.updateHealthStatus(healthStatus);

            // 如果系统不健康，尝试自动恢复
            if (healthStatus.overall !== 'good') {
                this.attemptAutoRecovery(healthStatus);
            }

            this.healthMonitor.lastHealthCheck = currentTime;

            // 定期报告健康状态
            if (this.performanceStats.totalProcessed % 50 === 0) {
                this.reportHealthStatus();
            }

        } catch (error) {
            mobileLog(`健康检查失败: ${error.message}`, 'error');
            this.healthMonitor.consecutiveErrors++;
        }
    }

    /**
     * 检查系统健康状态
     */
    checkSystemHealth() {
        const status = {
            overall: 'good',
            issues: [],
            metrics: {}
        };

        // 检查内存使用
        const queueSize = this.clickQueue.length;
        if (queueSize > this.maxQueueSize * 0.8) {
            status.issues.push('队列接近满载');
            status.overall = 'warning';
        }
        if (queueSize >= this.maxQueueSize) {
            status.issues.push('队列已满');
            status.overall = 'critical';
        }

        // 检查错误率
        const errorRate = this.performanceStats.totalProcessed > 0 ?
            this.performanceStats.totalErrors / this.performanceStats.totalProcessed : 0;
        if (errorRate > 0.1) { // 错误率超过10%
            status.issues.push(`错误率过高: ${(errorRate * 100).toFixed(1)}%`);
            status.overall = 'warning';
        }
        if (errorRate > 0.3) { // 错误率超过30%
            status.overall = 'critical';
        }

        // 检查处理速度
        if (this.performanceStats.avgProcessingTime > 10000) { // 平均处理时间超过10秒
            status.issues.push('处理速度过慢');
            status.overall = status.overall === 'good' ? 'warning' : status.overall;
        }

        // 检查连续错误
        if (this.healthMonitor.consecutiveErrors > 3) {
            status.issues.push('连续错误过多');
            status.overall = 'critical';
        }

        // 检查定时器数量
        const timerStats = this.getTimerStats();
        const totalTimers = timerStats.timeouts.count + timerStats.intervals.count;
        if (totalTimers > 20) {
            status.issues.push('定时器数量过多');
            status.overall = status.overall === 'good' ? 'warning' : status.overall;
        }

        // 记录指标
        status.metrics = {
            queueSize,
            errorRate,
            avgProcessingTime: this.performanceStats.avgProcessingTime,
            totalTimers,
            consecutiveErrors: this.healthMonitor.consecutiveErrors
        };

        return status;
    }

    /**
     * 更新健康状态
     */
    updateHealthStatus(healthStatus) {
        const previousHealth = this.healthMonitor.systemHealth;
        this.healthMonitor.systemHealth = healthStatus.overall;

        // 如果健康状态发生变化，记录日志
        if (previousHealth !== healthStatus.overall) {
            mobileLog(`系统健康状态变化: ${previousHealth} -> ${healthStatus.overall}`,
                healthStatus.overall === 'good' ? 'success' : 'warning');
        }

        // 更新警报
        this.healthMonitor.alerts = healthStatus.issues;
    }

    /**
     * 尝试自动恢复
     */
    attemptAutoRecovery(healthStatus) {
        mobileLog(`尝试自动恢复，问题: ${healthStatus.issues.join(', ')}`, 'warning');

        try {
            // 清理过期目标
            if (healthStatus.issues.some(issue => issue.includes('队列'))) {
                const removed = this.cleanupExpiredTargets(30000); // 清理30秒以上的目标
                if (removed > 0) {
                    mobileLog(`自动恢复: 清理了 ${removed} 个过期目标`, 'info');
                }
            }

            // 强制内存清理
            if (healthStatus.issues.some(issue => issue.includes('内存') || issue.includes('定时器'))) {
                this.performMemoryCleanup();
                mobileLog('自动恢复: 执行了内存清理', 'info');
            }

            // 重置连续错误计数
            if (this.healthMonitor.consecutiveErrors > 0) {
                this.healthMonitor.consecutiveErrors = Math.max(0, this.healthMonitor.consecutiveErrors - 1);
            }

        } catch (error) {
            mobileLog(`自动恢复失败: ${error.message}`, 'error');
        }
    }

    /**
     * 报告健康状态
     */
    reportHealthStatus() {
        const uptime = this.taskStartTime ? Date.now() - this.taskStartTime : 0;
        const uptimeStr = Math.round(uptime / 1000);

        mobileLog(`=== 系统健康报告 ===`, 'info');
        mobileLog(`运行时间: ${uptimeStr}秒`, 'info');
        mobileLog(`健康状态: ${this.healthMonitor.systemHealth}`, 'info');
        mobileLog(`处理总数: ${this.performanceStats.totalProcessed}`, 'info');
        mobileLog(`错误总数: ${this.performanceStats.totalErrors}`, 'info');
        mobileLog(`队列长度: ${this.clickQueue.length}/${this.maxQueueSize}`, 'info');
        mobileLog(`平均处理时间: ${Math.round(this.performanceStats.avgProcessingTime)}ms`, 'info');

        if (this.healthMonitor.alerts.length > 0) {
            mobileLog(`当前警报: ${this.healthMonitor.alerts.join(', ')}`, 'warning');
        }

        mobileLog(`==================`, 'info');
    }

    /**
     * 获取完整的系统状态
     */
    getSystemStatus() {
        return {
            isRunning: this.isTaskRunning,
            health: this.healthMonitor.systemHealth,
            alerts: this.healthMonitor.alerts,
            performance: this.performanceStats,
            queue: this.getQueueStats(),
            timers: this.getTimerStats(),
            uptime: this.taskStartTime ? Date.now() - this.taskStartTime : 0
        };
    }

}