import Config from "@/config";
import {mobileLog} from "@/services/RobotArmService";

/**
 * 图片识别服务
 * 负责处理图片上传、YOLO识别等功能
 */
export class ImageRecognitionService {
    constructor() {
        this.takePhoto = null;

        // 图片压缩配置
        this.compressionConfig = {
            quality: 0.85,        // JPEG质量 (0.1-1.0)，0.85适合YOLO识别
            enableCompression: true // 是否启用压缩
        };
    }


    /**
     * 拍照并进行YOLO识别
     * @param {Function} callback 识别完成后的回调函数
     */
    async captureAndRecognize(callback) {

        uni.$emit('takePhoto', {
            compressionConfig: this.compressionConfig,
            callback: phoneData => {
                mobileLog('拍照完成，开始异步上传和识别', 'info');

                // 直接上传处理后的图片数据（已在 takePhoto 中完成压缩）
                this.uploadAndRecognize(phoneData)
                    .then(result => {
                        callback(result);
                    })
                    .catch(error => {
                        mobileLog(`识别过程出错: ${error.message}`, 'error');
                        callback(null);
                    });
            }
        })
    }


    /**
     * 上传图片并进行YOLO识别
     * @param {Blob} blob 图片Blob对象
     * @returns {Promise<Object|null>} 识别结果
     */
    async uploadAndRecognize(blob) {
        try {

            // 创建FormData并上传到后端
            const formData = new FormData();
            formData.append('file', blob, 'photo.jpg'); // 压缩后为JPEG格式

            const protocol = Config.useSSL ? 'https' : 'http';
            const apiUrl = `${protocol}://${Config.host}:${Config.port}/yolo/detect`;

            const response = await fetch(apiUrl, {
                method: 'POST', body: formData
            });

            if (response.ok) {
                const result = await response.json();

                // 显示识别结果
                const detectedCount = result.results ? result.results.length : 0;
                mobileLog(`YOLO识别完成: 检测到${detectedCount}个目标;` + JSON.stringify(result), detectedCount > 0 ? 'success' : 'info');


                return result;
            } else {
                mobileLog(`YOLO识别失败: ${response.statusText}`, 'error');
                return null;
            }
        } catch (error) {
            mobileLog(`上传图片失败: ${error.message}`, 'error');
            throw error;
        }
    }
}