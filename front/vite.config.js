import {
	defineConfig
} from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import fs from 'node:fs'



const path = require('path');



export default defineConfig({
	plugins: [
		uni(),
	],
	server: {
		open: true,
		https: {
			// mkcert localhost *********** ************* ::1 创建证书
			// mkcert.exe -CAROOT  查看根证书目录
			cert: fs.readFileSync(path.resolve(__dirname, 'ssl/localhost+3.pem')),
			key: fs.readFileSync(path.resolve(__dirname, 'ssl/localhost+3-key.pem'))
		}
	}
});