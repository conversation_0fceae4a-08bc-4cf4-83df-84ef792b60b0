<template>
  <view class="take-phone-container">
    <view id="cameraContainer">

    </view>
  </view>

</template>

<script>

import Config from "@/config";
import {TakePhoneSocket} from "@/services/websocket/TakePhoneSocket";
import {mobileLog} from "@/services/RobotArmService";


let videoEl = null
let canvasEl = null;
let canvasCtx = null;


export default {
  name: "TakePhoto",
  props: {
    websocketService: {
      type: Object,
      required: true
    },
    deviceId: {
      type: String,
      required: true
    }
  },
  data() {
    return {};
  },
  mounted() {
    this.openCamera()

    // 监听拍照指令
    TakePhoneSocket.onTakePhone(this.websocketService, this.deviceId, () => {
      uni.showToast({title: '收到拍照指令', icon: 'success'});
      this.takePhoto()
    })

    uni.$on('takePhoto', (res) => {
      mobileLog('收到拍照事件')
      this.takePhoto(res.callback, res.compressionConfig);
    })

  },
  methods: {
    openCamera() {
      navigator.mediaDevices.getUserMedia({
        video: {
          width: Config.canvasResolution.x,
          height: Config.canvasResolution.y,
          facingMode: "environment",
          frameRate: 15
        },
      }).then(stream => {
        // 动态创建 video
        videoEl = document.createElement('video');
        videoEl.autoplay = true;
        videoEl.playsInline = true;
        videoEl.srcObject = stream;
        videoEl.style.margin = '0';
        videoEl.style.padding = '0';
        videoEl.style.width = '100%';
        videoEl.style.height = 'auto';
        videoEl.style.background = 'red';
        videoEl.style.objectFit = 'contain';

        // 插入到容器
        const container = document.getElementById('cameraContainer');
        container.innerHTML = '';
        container.appendChild(videoEl);
      }).catch(_err => {
        uni.showToast({title: '无法访问摄像头', icon: 'none'});
      });
    },
    takePhoto(callback = null, compressionConfig = null) {
      if (!videoEl) {
        this.openCamera();
      }
      if (!canvasCtx) {
        canvasEl = document.createElement('canvas');
        canvasEl.width = Config.canvasResolution.y;
        canvasEl.height = Config.canvasResolution.x;
        canvasCtx = canvasEl.getContext('2d');
      }

      canvasCtx.drawImage(videoEl, 0, 0, canvasEl.width, canvasEl.height);


      // 检查是否需要压缩
      if (compressionConfig && compressionConfig.enableCompression) {
        this.compressAndCallback(canvasEl, compressionConfig, callback);
      } else {
        canvasEl.toBlob((blob) => {
          // 使用WebSocket服务发送照片数据到PC端
          TakePhoneSocket.sendPhoneToPC(this.websocketService, blob, this.deviceId);

          if (callback) {
            callback(blob)
          }
        })
      }
    },

    /**
     * 压缩图片并执行回调
     * @param {HTMLCanvasElement} sourceCanvas 源canvas
     * @param {Object} compressionConfig 压缩配置
     * @param {Function} callback 回调函数
     */
    compressAndCallback(sourceCanvas, compressionConfig, callback) {
      try {
        // 直接转换格式和质量，不调整尺寸
        sourceCanvas.toBlob((compressedBlob) => {
          if (compressedBlob) {
            console.log('图片已压缩，保持原始尺寸');
            // 使用WebSocket服务发送照片数据到PC端
            TakePhoneSocket.sendPhoneToPC(this.websocketService, compressedBlob, this.deviceId);

            if (callback) {
              callback(compressedBlob);
            }
          } else {
            console.error('图片压缩失败');
            // 压缩失败，使用原图
            this.fallbackToOriginal(sourceCanvas, callback);
          }
        }, 'image/jpeg', compressionConfig.quality);
      } catch (error) {
        console.error('压缩过程出错:', error);
        // 出错时使用原图
        this.fallbackToOriginal(sourceCanvas, callback);
      }
    },

    /**
     * 压缩失败时的回退方案，使用原图
     * @param {HTMLCanvasElement} sourceCanvas 源canvas
     * @param {Function} callback 回调函数
     */
    fallbackToOriginal(sourceCanvas, callback) {
      sourceCanvas.toBlob((blob) => {
        // 使用WebSocket服务发送照片数据到PC端
        TakePhoneSocket.sendPhoneToPC(this.websocketService, blob, this.deviceId);

        if (callback) {
          callback(blob);
        }
      });
    },


  }
}
</script>

<style scoped lang="scss">


.take-phone-container {
  position: relative;
  z-index: 0;


  #cameraContainer {
    position: relative;
    z-index: 1;
    padding: 0;
    margin: 0;
  }

}
</style>
