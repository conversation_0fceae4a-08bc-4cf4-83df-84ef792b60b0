<template>
  <view class="container">
    <template v-if="!selectedDeviceId">
      <view class="title">请选择设备ID：</view>
      <picker :range="deviceList" @change="onSelect">
        <view class="picker">
          {{ selectedDeviceId ? selectedDeviceId : '请选择设备' }}
        </view>
      </picker>
      <view class="btn" @click="navigateToPage('/pages/calibration/calibration-target')">校准标记页面</view>
    </template>
    <template v-else>
      <view class="main-solt">
        <view class="head">
          <view>当前设备ID：{{ selectedDeviceId }}</view>
          <view class="btn" @click="selectedDeviceId = ''">切换设备ID</view>
        </view>
        <slot></slot>
      </view>
    </template>
  </view>
</template>

<script>

import config from "@/config.js";
import {ConfigService} from "@/services/ConfigService";

export default {
  name: "SetDeviceId",
  data() {
    return {
      deviceList: config.deviceIds, // 可替换为实际设备ID
      selectedDeviceId: ''
    };
  },
  mounted() {
    let deviceId = ConfigService.getDeviceId()
    if (deviceId) {
      this.selectedDeviceId = deviceId
      this.$nextTick(() => {
        this.$emit('selected', deviceId)
      })
    }
  },
  methods: {
    navigateToPage(path) {
      if (path === 'ca') {
        window.location.href = "/static/rootCA.pem"
      } else {
        uni.navigateTo({
          url: path
        });
      }
    },
    onSelect(e) {
      const deviceId = this.deviceList[e.detail.value]
      ConfigService.setDeviceId(deviceId)
      this.selectedDeviceId = deviceId;
      this.$emit('selected', deviceId)
    },
  }
}
</script>

<style scoped lang="scss">
.title {
  font-size: 18px;
  margin-bottom: 20px;
}

.picker {
  padding: 10px 20px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 20px;
  min-width: 120px;
  text-align: center;
}

.main-solt {
  position: relative;

  .head {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }
}

</style>