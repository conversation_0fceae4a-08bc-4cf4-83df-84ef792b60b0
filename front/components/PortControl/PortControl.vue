<template>
  <view class="port-control-container">
    <text class="label">端口：</text>
    <picker :range="portList" v-model="portIndex" @change="onPortChange" :disabled="handle>0">
      <view class="picker-view" :class="{ disabled: handle>0 }">{{ portList[portIndex] }}</view>
    </picker>

    <view v-if="handle>0" class="service-status">
      <text class="text">句柄号 - {{ handle }}</text>
    </view>

    <view class="btn" v-if="handle===0" @click="openHandle">打开端口</view>
    <view class="btn error" v-if="handle>0" @click="closeHandle">关闭端口</view>
    <view class="btn warn" v-if="handle>0" @click="reset">复位</view>

  </view>
</template>

<script>

import {RobotArmSocket} from "@/services/websocket/RobotArmSocket";

export default {
  name: 'PortControl',
  props: {
    deviceId: {
      type: String,
      required: true
    },
    websocketService: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      portList: [
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5',
        'COM6', 'COM7', 'COM8', 'COM9', 'COM10'
      ],
      portIndex: 3, // 默认COM4
      handle: 0, // 端口状态

      robotArmService: null

    };
  },
  computed: {
    port() {
      return this.portList[this.portIndex];
    },
  },
  mounted() {
    // 获取当前设备ID是否有缓存的port
    const cachedPort = uni.getStorageSync(`deviceId:${this.deviceId}`);
    if (cachedPort) {
      // 查找缓存的port在portList中的索引
      const cachedIndex = this.portList.findIndex(port => port === cachedPort);
      if (cachedIndex !== -1) {
        this.portIndex = cachedIndex;
      }
    }
    this.getRobotArmStatus();
  },
  methods: {
    onPortChange(e) {
      this.portIndex = e.detail.value;
    },
    openHandle() {
      RobotArmSocket.notifyOpenHandle(this.websocketService, this.deviceId, this.port, res => {
        this.handle = res.json.handle
        uni.$emit('robotStatusChange', {
          handle: res.json.handle,
          port: res.json.port,
          deviceId: this.deviceId,
        })
      })
    },
    async closeHandle() {
      RobotArmSocket.notifyCloseHandle(this.websocketService, this.deviceId, _res => {
        this.handle = 0;
        uni.$emit('robotStatusChange', {
          handle: 0,
          port: '',
          deviceId: this.deviceId,
        })
      })
    },
    async reset() {
      RobotArmSocket.notifyReset(this.websocketService, this.deviceId, (r) => {
        uni.showToast({
          title: r ? '重置成功' : '重置失败',
          icon: r ? 'success' : 'error',
        })
      })
    },

    getRobotArmStatus() {
      RobotArmSocket.getStatus(this.websocketService, this.deviceId, (res) => {
        this.handle = res.handle
        uni.$emit('robotStatusChange', res)
      })
    },

  },
};
</script>

<style scoped lang="scss">
.port-control-container {
  margin-bottom: 10rpx;
  background-color: white;
  padding: 10px;
  border-radius: 10px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;

  .picker-view {
    border: 1px solid #eeeeee;
    padding: 3px 10px;
  }

  text {
    font-size: 12px;
  }
}
</style>