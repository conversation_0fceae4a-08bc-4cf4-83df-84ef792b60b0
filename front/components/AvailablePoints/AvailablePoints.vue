<template>
  <view class="available-points-section">
    <view class="section-title">
      <text>可点击的点 (来自校准标记页)</text>
    </view>
    <view class="points-display" v-if="availablePoints.length > 0">
      <view class="points-grid">
        <view
            class="point-item"
            v-for="point in availablePoints"
            :key="point"
            :class="{ 'selected': selectedPoint === point }"
            @click="selectPoint(point)"
        >
          <text class="point-number">{{ point.index }}</text>
          <text class="point-status">{{ point.x.toFixed(2) }},{{point.y.toFixed(2)}}</text>

        </view>
      </view>
    </view>
    <button class="btn small-btn" v-if="availablePoints.length===0" @click="requestTouchPoints">
      获取可点击点
    </button>
  </view>
</template>

<script>
export default {
  name: 'AvailablePoints',
  props: {
    websocketService: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      availablePoints: [],
      selectedPoint: null
    };
  },
  mounted() {
    this.initWebSocketListeners();
  },
  methods: {
    initWebSocketListeners() {
      if (!this.websocketService) {
        console.warn('WebSocket服务未提供');
        return;
      }

      // 监听touch-points数据
      this.websocketService.on('touch-points', (res) => {
        console.log('收到touch-points数据:', res);
        this.availablePoints = res.json.points || [];
      });
    },

    selectPoint(point) {
      this.selectedPoint = point;
      console.log('选择了点:', point.index);
      // 向父组件发送选择的点
      this.$emit('pointSelected', point);
    },


    // 主动请求获取touch-points数据
    requestTouchPoints() {
      if (!this.websocketService) {
        uni.showToast({title: 'WebSocket未连接', icon: 'error'});
        return;
      }

      console.log('主动请求touch-points数据');
      this.websocketService.send('request-touch-points');
    }
  }
};
</script>

<style scoped>
/* 可点击点显示样式 */
.available-points-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.points-display {
  background: #fff;
  border-radius: 4px;
  padding: 15px;
  border: 1px solid #e0e0e0;
}

.points-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.point-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 2px solid #007bff;
  border-radius: 8px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.point-item:hover {
  background-color: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

.point-item.selected {
  background-color: #007bff;
  color: #fff;
  border-color: #0056b3;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.point-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.point-status {
  font-size: 12px;
  text-align: center;
}

.point-item.selected .point-status {
  color: #fff;
}


.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  margin-right: 10px;
  margin-bottom: 10px;
}

.btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.btn.small-btn {
  padding: 6px 12px;
  font-size: 14px;
}
</style>