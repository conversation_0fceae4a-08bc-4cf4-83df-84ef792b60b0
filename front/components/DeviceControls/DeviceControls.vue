<template>
  <view class="device-controls" v-if="websocketService && websocketService.connectionStatus === 'connected'">
    <button class="btn small-btn" @click="refreshMobileList">刷新手机列表</button>
    <view class="mobile-list" v-if="availableMobiles.length > 0">
      <text class="label">可用手机设备:</text>
      <view class="mobile-item" v-for="mobile in availableMobiles" :key="mobile">
        <text class="mobile-id">{{ mobile }}</text>
        <button class="btn small-btn" @click="takePhoto(mobile)" :disabled="isWaitingPhoto">
          {{ isWaitingPhoto ? '等待拍照中...' : '远程拍照' }}
        </button>
      </view>
    </view>
    <text v-else class="no-mobile-tip">暂无可用的手机设备</text>
  </view>
</template>

<script>

import {TakePhoneSocket} from "@/services/websocket/TakePhoneSocket";
import {AvailableMobileSocket} from "@/services/websocket/AvailableMobileSocket";

export default {
  name: 'DeviceControls',
  props: {
    websocketService: {
      type: Object,
      required: true
    },
    onPhotoReceived: {
      type: Function
    }
  },
  data() {
    return {
      availableMobiles: [],
      isWaitingPhoto: false,
      currTakePhotoDeviceId: null,
    }
  },
  watch: {
    'websocketService.connectionStatus'(newStatus) {
      if (newStatus === 'connected') {
        console.log('连接状态变为已连接，刷新手机列表');
        this.refreshMobileList();
      }
    }
  },
  mounted() {
    this.initWebSocketListeners();
  },
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    initWebSocketListeners() {
      if (!this.websocketService) {
        console.error('WebSocket服务未提供');
        return;
      }

      // 监听可用手机设备列表
      AvailableMobileSocket.onAvailableMobile(this.websocketService, deviceId => {
        this.availableMobiles.push(deviceId);
      })

      TakePhoneSocket.receivePhoneByMobile(this.websocketService, (photoData, deviceId) => {
        if (deviceId === this.currTakePhotoDeviceId) {
          this.isWaitingPhoto = false;
        }
      })
    },


    refreshMobileList() {
      console.log('刷新手机列表');
      try {
        AvailableMobileSocket.getAvailableMobiles(this.websocketService);
        this.availableMobiles = [];
        console.log('已发送获取可用手机列表请求');
      } catch (error) {
        console.error('发送获取可用手机列表请求失败:', error);
        uni.showToast({title: '获取手机列表失败', icon: 'error'});
      }
    },

    takePhoto(mobileId) {
      this.isWaitingPhoto = true;

      TakePhoneSocket.sendTakePhone(this.websocketService, mobileId)
      this.currTakePhotoDeviceId = mobileId;

    }
  }
}
</script>

<style lang="scss" scoped>
.device-controls {
  margin-bottom: 20rpx;
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.mobile-list {
  margin-top: 15rpx;
}

.mobile-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 5rpx;
  margin-bottom: 10rpx;
}

.mobile-id {
  font-size: 26rpx;
  color: #333;
}

.no-mobile-tip {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  padding: 20rpx;
}

.btn {
  background-color: #007AFF;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  margin: 10rpx;

  &:disabled {
    background-color: #ccc;
  }
}

.small-btn {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  margin: 5rpx;
}

.label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}
</style>