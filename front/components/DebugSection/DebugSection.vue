<template>
  <view class="debug-section">
    <view class="debug-header">
      <text class="debug-title">调试信息</text>
      <view class="debug-controls">
        <button class="clear-btn" @click="clearDebugLogs">清空</button>
        <button class="toggle-btn" @click="toggleDebugSection">{{ debugSectionVisible ? '隐藏' : '显示' }}</button>
      </view>
    </view>
    <scroll-view
        v-if="debugSectionVisible"
        class="debug-logs"
        scroll-y="true"
        :scroll-top="scrollTop"
    >
      <view
          v-for="(log, index) in debugLogs"
          :key="index"
          class="log-item"
          :class="log.type">
        <text class="log-time">{{ log.time }}</text>
        <text class="log-content">{{ log.message }}</text>
      </view>
      <view v-if="debugLogs.length === 0" class="no-logs">
        <text>暂无调试信息</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: "DebugSection",
  data() {
    return {
      debugLogs: [],
      debugSectionVisible: true,
      scrollTop: 0,
      maxLogCount: 200, // 最大日志条目数，防止内存溢出
    };
  },
  mounted() {
    // 监听TaskRunner的调试事件
    uni.$on('taskRunner:debug', (data) => {
      this.addDebugLog(data.message, data.type);
    });
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off('taskRunner:debug');
  },
  methods: {
    // 添加调试日志
    addDebugLog(message, type = 'info') {
      const now = new Date();
      const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

      const logItem = {
        time: timeStr,
        message: message,
        type: type // info, success, error, warning
      };

      this.debugLogs.push(logItem);

      // 限制日志条目数量，防止内存溢出
      if (this.debugLogs.length > this.maxLogCount) {
        this.debugLogs.splice(0, this.debugLogs.length - this.maxLogCount);
      }

      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 清空调试日志
    clearDebugLogs() {
      this.debugLogs = [];
      this.scrollTop = 0;
    },

    // 切换调试区域显示/隐藏
    toggleDebugSection() {
      this.debugSectionVisible = !this.debugSectionVisible;
    },

    // 滚动到底部
    scrollToBottom() {
      this.scrollTop = this.debugLogs.length * 50; // 估算高度
    },

  }
}
</script>

<style scoped lang="scss">
.debug-section {
  margin-top: 30rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 20rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.debug-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
}

.debug-controls {
  display: flex;
  gap: 10rpx;
}

.clear-btn, .toggle-btn {
  font-size: 22rpx;
  border-radius: 6rpx;
  border: none;
  cursor: pointer;
}

.clear-btn {
  background-color: #dc3545;
  color: white;
}

.toggle-btn {
  background-color: #007bff;
  color: white;
}

.debug-logs {
  height: 400rpx;
  background-color: #f8f9fa;
}

.log-item {
  display: flex;
  padding: 15rpx 20rpx;
  border-bottom: 1rpx solid #e9ecef;
  font-size: 22rpx;
  line-height: 1.4;
}

.log-item.info {
  background-color: #ffffff;
}

.log-item.success {
  background-color: #d4edda;
  color: #155724;
}

.log-item.error {
  background-color: #f8d7da;
  color: #721c24;
}

.log-item.warning {
  background-color: #fff3cd;
  color: #856404;
}

.log-time {
  flex-shrink: 0;
  width: 120rpx;
  color: #6c757d;
  font-family: monospace;
}

.log-content {
  flex: 1;
  margin-left: 20rpx;
  word-break: break-all;
}

.no-logs {
  padding: 60rpx 20rpx;
  text-align: center;
  color: #6c757d;
  font-size: 24rpx;
}
</style>