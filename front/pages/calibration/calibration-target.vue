<template>
  <view class="calibration-target" @click="handleBackgroundTap">
    <view class="grid-container">
      <view class="grid-point top-left" @click.stop="handlePointTap(1, $event)">1</view>
      <view class="grid-point top-right" @click.stop="handlePointTap(2, $event)">2</view>
      <view class="grid-point bottom-left" @click.stop="handlePointTap(3, $event)">3</view>
      <view class="grid-point bottom-right" @click.stop="handlePointTap(4, $event)">4</view>
    </view>
    <view class="connect-status">
      <ConnectionStatus :websocketService="websocketService"/>
    </view>

  </view>
</template>

<script>

import {WebsocketService} from "@/services/WebsocketService";

export default {
  data() {
    return {
      deviceId: 'calibration-target',
      websocketService: null,
    };
  },
  onLoad() {
    this.initWebSocket();
  },
  methods: {
    initWebSocket() {
      this.websocketService = new WebsocketService();
      this.websocketService.init(this.deviceId);

      this.websocketService.on('connected', () => {
        this.sendTouchPointsData()
      })

      // 监听touch-points请求
      this.websocketService.on('request-touch-points', (message) => {
        console.log('收到touch-points请求:', message);
        this.sendTouchPointsData();
      });

    },


    handleBackgroundTap(event) {
      console.log(event);
      const touch = event.changedTouches[0];
      const tapCoords = {
        x: touch.clientX,
        y: touch.clientY
      };
      this.sendTapEvent(tapCoords, false);
    },
    handlePointTap(index, event) {
      console.log(event);
      const touch = event.changedTouches[0];
      const tapCoords = {
        x: touch.clientX,
        y: touch.clientY
      };
      this.sendTapEvent(tapCoords, true, index);
    },
    sendTapEvent(coords, isHit, pointIndex = -1) {
      if (this.websocketService) {
        this.websocketService.send('screen_tap', {
          x: coords.x,
          y: coords.y,
          isHit: isHit,
          pointIndex: pointIndex,
        });
      }
    },

    // 发送touch-points数据
    sendTouchPointsData() {
      // 获取四个角点元素
      const topLeftEl = document.querySelector('.top-left');
      const topRightEl = document.querySelector('.top-right');
      const bottomLeftEl = document.querySelector('.bottom-left');
      const bottomRightEl = document.querySelector('.bottom-right');

      // 获取每个点在屏幕上的位置
      const topLeftRect = topLeftEl.getBoundingClientRect();
      const topRightRect = topRightEl.getBoundingClientRect();
      const bottomLeftRect = bottomLeftEl.getBoundingClientRect();
      const bottomRightRect = bottomRightEl.getBoundingClientRect();

      // 计算每个点的中心位置
      const points = [
        {
          pos: 'topLeft',
          index: 1,
          x: topLeftRect.left + topLeftRect.width / 2,
          y: topLeftRect.top + topLeftRect.height / 2
        },
        {
          pos: 'topRight',
          index: 2,
          x: topRightRect.left + topRightRect.width / 2,
          y: topRightRect.top + topRightRect.height / 2
        },
        {
          pos: 'bottomLeft',
          index: 3,
          x: bottomLeftRect.left + bottomLeftRect.width / 2,
          y: bottomLeftRect.top + bottomLeftRect.height / 2
        },
        {
          pos: 'bottomRight',
          index: 4,
          x: bottomRightRect.left + bottomRightRect.width / 2,
          y: bottomRightRect.top + bottomRightRect.height / 2
        }
      ];

      console.log('四个角点的屏幕位置:', points);

      this.websocketService.send('touch-points', {
        points:points
      });
    }
  },
};
</script>

<style>
.calibration-target {
  width: 100vw;
  height: 100vh;
  background-color: #2c3e50;
  position: relative;
  overflow: hidden;
}

.grid-container {
  position: relative;
  width: 100vw;
  height: 84vh;
  z-index: 99;
}

.grid-point {
  position: absolute;
  width: 15px;
  height: 15px;
  background-color: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  border: 1px solid white;
  cursor: pointer;
}

.top-left {
  top: 0;
  left: 0;
}

.top-right {
  top: 0;
  right: 0;
}

.bottom-left {
  bottom: 0;
  left: 0;
}

.bottom-right {
  right: 0;
  bottom: 0;
}

.connect-status {
  position: fixed;
  z-index: 1;
  top: 15%;

}


</style>