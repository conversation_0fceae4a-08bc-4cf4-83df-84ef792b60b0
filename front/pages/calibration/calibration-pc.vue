<template>
  <view class="container">


    <!-- 设备连接状态 -->
    <ConnectionStatus :websocketService="websocketService"/>


    <template v-if="websocketService">
      <DeviceControls
          :websocketService="websocketService"
      />
    </template>


    <view class="photo-display" v-if="photoData">
      <canvas
          canvas-id="photoCanvas"
          id="photoCanvas"
          class="photo-canvas"
          :style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
          @tap="onCanvasTap"
      ></canvas>
    </view>

    <view class="port-control-warp" v-if="currTakePhotoDeviceId">
      <PortControl :device-id="currTakePhotoDeviceId" :websocket-service="websocketService"/>
    </view>


    <!-- 可点击的点显示 -->
    <template v-if="websocketService">
      <AvailablePoints
          :websocketService="websocketService"
          @pointSelected="onPointSelected"
      />
    </template>


    <!-- 校准控制 -->
    <view class="calibration-section" v-if="photoData">
      <view class="section-title">
        <text>校准控制</text>
      </view>

      <view class="calibration-info">
        <text class="info-text">请确保手机显示校准标记页面，然后开始校准流程</text>
      </view>


      <view class="calibration-controls" v-if="
       websocketService.connectionStatus==='connected'
       && selectedPoint
       && robotHandle
       && canvasService && canvasService.getClickMarkers().length>0
">
        <button class="btn" @click="startCalibration" :disabled="calibrationService.isCalibrating">
          {{ calibrationService.isCalibrating ? '校准中...' : '开始校准' }}
        </button>
        <button class="btn danger" @click="stopCalibration" v-if="calibrationService.isCalibrating">
          停止校准
        </button>

        <button class="btn" @click="moveJxb">
          检验校准
        </button>

      </view>

      <!-- 校准结果显示 -->
      <view class="calibration-result" v-if="calibrationOffsetResult.isVisible">
        <view class="result-title">
          <text>校准结果</text>
        </view>
        <view class="result-content">
          <view class="offset-input-group">
            <view class="input-item">
              <text class="input-label">偏移X:</text>
              <input
                  v-model="calibrationOffsetResult.offsetX"
                  type="number"
                  step="0.01"
                  class="offset-input"
                  placeholder="请输入X偏移值"
              />
            </view>
            <view class="input-item">
              <text class="input-label">偏移Y:</text>
              <input
                  v-model="calibrationOffsetResult.offsetY"
                  type="number"
                  step="0.01"
                  class="offset-input"
                  placeholder="请输入Y偏移值"
              />
            </view>
          </view>
          <button class="btn save-btn" @click="saveOffsetConfig">保存偏移配置</button>
        </view>
      </view>

    </view>


    <!-- 点击事件日志 -->
    <view class="click-log-section">
      <view class="section-title">
        <text>点击事件日志 (来自校准标记页)</text>
      </view>
      <scroll-view scroll-y="true" class="log-scroll-view">
        <view v-if="clickEvents.length === 0" class="log-empty">
          <text>暂无点击事件</text>
        </view>
        <view v-else class="log-list">
          <view class="log-item" v-for="(event, index) in clickEvents" :key="index">
            <view class="log-content">
              <text class="log-coords">坐标: ({{ event.x.toFixed(2) }}, {{ event.y.toFixed(2) }})</text>
              <text class="log-robot-pos" v-if="event.robotPos">机械臂: ({{ event.robotPos.x.toFixed(2) }},
                {{ event.robotPos.y.toFixed(2) }})
              </text>
            </view>
            <text :class="['log-hit', event.isHit ? 'hit' : '']">{{ event.isHit ? '命中点位' : '未命中' }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 操作提示 -->
    <view class="tips">
      <text>提示：</text>
      <text>1. 确保手机显示校准标记页面</text>
      <text>2. 连接机械臂后开始校准</text>
      <text>3. 校准过程中机械臂会自动点击各个标记点</text>
      <text>4. 完成后保存校准结果用于后续操作</text>
    </view>
  </view>
</template>

<script>
import {WebsocketService} from "@/services/WebsocketService";
import AvailablePoints from "@/components/AvailablePoints/AvailablePoints.vue";
import {CountService} from "@/services/CountService";
import {calibrationService} from "@/services/CalibrationService";
import Config from "@/config";
import {CanvasService} from "@/services/CanvasService";
import {ConfigWebsocketService} from "@/services/websocket/ConfigWebsocketService";
import {TakePhoneSocket} from "@/services/websocket/TakePhoneSocket";
import {RobotArmSocket} from "@/services/websocket/RobotArmSocket";


export default {
  name: "CalibrationPC",
  components: {
    AvailablePoints
  },
  data() {
    return {

      websocketService: null,

      // 连接状态
      deviceId: 'pc_calibration_center',

      currTakePhotoDeviceId: null,


      // 校准服务实例
      calibrationService: calibrationService,


      // 校准偏移结果
      calibrationOffsetResult: {
        offsetX: null,
        offsetY: null,
        isVisible: false
      },


      // 照片相关
      photoData: '',
      canvasWidth: Config.canvasResolution.x,
      canvasHeight: Config.canvasResolution.y,

      // Canvas服务实例
      canvasService: null,

      robotHandle: 0,
      // 点击事件日志
      clickEvents: [],

      // 当前选择的点（从子组件接收）
      selectedPoint: null,
    };
  },


  mounted() {
    this.initWebSocket();
    uni.$on('robotStatusChange', res => {
      if (res.handle) {
        this.robotHandle = res.handle;
      }
    })
  },

  methods: {
    initWebSocket() {
      this.websocketService = new WebsocketService();
      this.websocketService.init(this.deviceId);


      this.websocketService.on('screen_tap', (res) => {
        console.log('收到屏幕点击:', res);
        console.log('需要点中:', this.selectedPoint);

        // 在点击事件数据中添加当前机械臂位置
        const robotStatus = this.calibrationService.getCalibrationStatus();
        const eventData = {
          ...res.json,
          robotPos: {
            x: robotStatus.robotPos.x,
            y: robotStatus.robotPos.y
          }
        };

        this.clickEvents.unshift(eventData);
        // 保持最多30条记录
        if (this.clickEvents.length > 30) {
          this.clickEvents.pop();
        }
      });

      this.websocketService.on('connected', () => {

      })

      TakePhoneSocket.receivePhoneByMobile(this.websocketService, (photoData, deviceId) => {

        if (!this.canvasService) {
          this.canvasService = new CanvasService('photoCanvas', this);
        }

        this.currTakePhotoDeviceId = deviceId
        this.photoData = URL.createObjectURL(photoData);

        // 使用回调确保canvas尺寸在图片加载完成后更新
        this.canvasService.drawPhotoOnCanvas(this.photoData, (canvasSize) => {
          this.canvasWidth = canvasSize.width;
          this.canvasHeight = canvasSize.height;
        });


      })


    },


    // Canvas点击事件处理
    onCanvasTap(e) {
      if (!this.photoData || this.calibrationService.isCalibrating) {
        return;
      }

      this.canvasService.handleCanvasTap(e, (clickMarkers) => {
        // 重绘canvas以显示新标记
        this.canvasService.drawPhotoOnCanvas(this.photoData);
      });
    },

    async startCalibration() {
      await this.calibrationService.startCalibration({
        selectedPoint: this.selectedPoint,
        deviceId: this.currTakePhotoDeviceId,
        websocketService: this.websocketService,
        clickEvents: this.clickEvents,
        onCalibrationSuccess: this.onCalibrationSuccess,
        onCalibrationStop: this.onCalibrationStop,
      });
    },


    moveJxb() {
      const clickMarkers = this.canvasService.getClickMarkers();
      if (clickMarkers.length === 0) {
        uni.showToast({title: '请先点击照片选择位置', icon: 'error'});
        return;
      }

      ConfigWebsocketService.getConfig(this.currTakePhotoDeviceId, this.websocketService, async (config) => {
        this.calibrationOffsetResult.offsetX = config.offsetX;
        this.calibrationOffsetResult.offsetY = config.offsetY;
        this.calibrationOffsetResult.isVisible = true;

        let moveX = clickMarkers[0].canvasX
        let moveY = clickMarkers[0].canvasY

        await RobotArmSocket.notifyMoveToByPx(this.websocketService, this.currTakePhotoDeviceId, moveX, moveY);
      });
    },


    // 校准成功回调
    onCalibrationSuccess(robotPos) {
      const clickMarkers = this.canvasService.getClickMarkers();
      if (clickMarkers.length === 0) {
        console.error('没有点击标记数据');
        return;
      }

      console.log(clickMarkers[0]);
      console.log(CountService.getPxWidth());
      const offsetX = robotPos.x - clickMarkers[0].canvasX / CountService.getPxWidth();
      const offsetY = robotPos.y - clickMarkers[0].canvasY / CountService.getPxHeight();

      console.log(offsetX, offsetY);

      // 保存并显示校准结果
      this.calibrationOffsetResult = {
        offsetX: Math.ceil(offsetX),
        offsetY: Math.ceil(offsetY),
        isVisible: true
      };

      ConfigWebsocketService.setConfig(this.currTakePhotoDeviceId, {
        offsetX,
        offsetY
      }, this.websocketService);
    },

    // 校准停止回调
    onCalibrationStop() {
      // 可以在这里添加停止校准后的处理逻辑
    },

    stopCalibration() {
      this.calibrationService.stopCalibration();
    },

    // 保存偏移配置
    saveOffsetConfig() {
      if (!this.currTakePhotoDeviceId) {
        uni.showToast({title: '请先拍照获取设备ID', icon: 'error'});
        return;
      }

      const offsetX = parseFloat(this.calibrationOffsetResult.offsetX);
      const offsetY = parseFloat(this.calibrationOffsetResult.offsetY);

      if (isNaN(offsetX) || isNaN(offsetY)) {
        uni.showToast({title: '请输入有效的偏移值', icon: 'error'});
        return;
      }

      ConfigWebsocketService.setConfig(this.currTakePhotoDeviceId, {
        offsetX,
        offsetY
      }, this.websocketService);
      uni.showToast({title: '偏移配置已保存', icon: 'success'});
    },


    // 选择点
    // 处理子组件选择点的事件
    onPointSelected(point) {
      this.selectedPoint = point;
      console.log('从子组件接收到选择的点:', point);
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20px;
  margin: 0 auto;
}


.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}


.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  margin-right: 10px;
  margin-bottom: 10px;
}

.port-control-warp {
  padding-top: 20px;
}

.btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}


.btn.danger {
  background-color: #dc3545;
}


.calibration-info {
  background-color: #e7f3ff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.info-text {
  color: #0066cc;
  font-size: 14px;
}

.calibration-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}


.offset-result text {
  display: block;
  margin-bottom: 5px;
  font-family: monospace;
}


.tips {
  background-color: #fff3cd;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
}

.tips text {
  display: block;
  margin-bottom: 5px;
  color: #856404;
}


.photo-display {
  position: relative;
  background-color: #fff;
  padding: 0;
  border: 1px solid #ddd;
  margin-top: 20px;
  overflow: auto;
}

.photo-canvas {
  border: 1px solid #ccc;
  cursor: crosshair;
  display: block;
  margin: 0 auto;
}

.click-log-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.log-scroll-view {
  max-height: 200px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 10px;
  background: #fff;
}

.log-empty {
  text-align: center;
  color: #999;
  padding: 20px 0;
}

.log-list {
  display: flex;
  flex-direction: column;
}

.log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 5px;
  border-bottom: 1px solid #f5f5f5;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 0 10px;
}

.log-coords {
  color: #333;
  margin-bottom: 2px;
}

.log-robot-pos {
  color: #666;
  font-size: 12px;
}

.log-hit {
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f0f0f0;
  color: #666;
  font-size: 12px;
  min-width: 60px;
  text-align: center;
}

.log-hit.hit {
  background-color: #d4edda;
  color: #155724;
  font-weight: bold;
}

.calibration-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 4px;
}

.result-title {
  font-size: 16px;
  font-weight: bold;
  color: #0c5460;
  margin-bottom: 10px;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.result-item {
  font-family: monospace;
  font-size: 14px;
  color: #0c5460;
  background-color: #ffffff;
  padding: 5px 10px;
  border-radius: 3px;
  border: 1px solid #b8daff;
}

.offset-input-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 15px;
}

.input-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-label {
  font-weight: bold;
  color: #0c5460;
  min-width: 60px;
  font-size: 14px;
}

.offset-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #b8daff;
  border-radius: 4px;
  font-size: 14px;
  background-color: #ffffff;
  color: #333;
}

.offset-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.save-btn {
  background-color: #28a745;
  margin-top: 10px;
  width: 100%;
}

.save-btn:hover {
  background-color: #218838;
}


</style>