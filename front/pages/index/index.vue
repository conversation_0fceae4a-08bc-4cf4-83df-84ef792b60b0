<template>
  <view class="container">
    页面跳转中...
  </view>
</template>

<script>

export default {

  data() {
    return {

    }
  },
  onLoad() {
    const deviceType = (() => {
      const ua = navigator.userAgent;
      const isMobile = /Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua);
      const isTablet = /(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua);

      if (isMobile) return isTablet ? "tablet" : "mobile";
      return "desktop";
    })();
    if(deviceType==='desktop'){
      this.navigateToPage('/pages/index/task-ctrl')
    }else{
      this.navigateToPage('/pages/index/camera-mobile')
    }
    console.log(`设备类型: ${deviceType}`);
  },
  methods: {
    navigateToPage(path) {
      uni.redirectTo({
        url: path
      });
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

</style>