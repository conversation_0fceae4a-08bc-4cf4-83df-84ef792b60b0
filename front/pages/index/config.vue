<template>
  <view class="config-container">
    <ConfigForm 
      title="物理尺寸配置" 
      :form-data="form" 
      @save="saveConfig"
      @form-change="onFormChange"
    />
  </view>
</template>

<script>
import ConfigForm from '@/components/ConfigForm/ConfigForm.vue';
import {ConfigService} from "@/services/ConfigService";

export default {
  components: {
    ConfigForm
  },
  data() {
    return {
      form: {
        physicalHeight: '',
        physicalWidth: '',
        offsetX: '',
        offsetY: '',
        maxMoveX: '',
        maxMoveY: '',
        yFirstRow: '',
        ySecondRow: '',
        xIncrement: ''
      }
    }
  },
  mounted() {
    this.loadConfig();
  },
  methods: {
    loadConfig() {
      // 使用configService统一加载配置
      this.form.physicalHeight = ConfigService.getPhysicalHeight();
      this.form.physicalWidth = ConfigService.getPhysicalWidth();
      this.form.offsetX = ConfigService.getOffsetX();
      this.form.offsetY = ConfigService.getOffsetY();
      this.form.maxMoveX = ConfigService.getMaxMoveX();
      this.form.maxMoveY = ConfigService.getMaxMoveY();
      this.form.yFirstRow = ConfigService.getYFirstRow();
      this.form.ySecondRow = ConfigService.getYSecondRow();
      this.form.xIncrement = ConfigService.getXIncrement();
      console.log('配置加载完成:', this.form);
    },
    onFormChange(formData) {
      this.form = { ...formData };
    },
    saveConfig(formData) {
      ConfigService.setPhysicalHeight(formData.physicalHeight);
      ConfigService.setPhysicalWidth(formData.physicalWidth);
      ConfigService.setOffsetX(formData.offsetX);
      ConfigService.setOffsetY(formData.offsetY);
      ConfigService.setMaxMoveX(formData.maxMoveX);
      ConfigService.setMaxMoveY(formData.maxMoveY);
      ConfigService.setYFirstRow(formData.yFirstRow);
      ConfigService.setYSecondRow(formData.ySecondRow);
      ConfigService.setXIncrement(formData.xIncrement);

      uni.showToast({title: '保存成功', icon: 'success'});
      console.log('配置保存完成');
    },
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  padding: 0;
}
</style>
