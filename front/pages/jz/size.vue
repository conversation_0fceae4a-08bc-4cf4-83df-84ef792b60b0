<template>
	<view class="container">
		<view class="header">
			<text class="title">照片尺寸测量</text>
		</view>

		<!-- 相机组件 -->
		<TakePhoto ref="takePhoto" @photoTaken="onPhotoTaken" />
		
		<view class="controls">
			<button class="btn" @click="takePhoto">拍照</button>
		</view>
		
		<!-- 参考物设置 -->
		<view class="reference-section" v-if="photoData">
			<view class="input-group">
				<text class="label">参考物实际长度 (mm):</text>
				<input class="input" type="number" v-model="referenceLength" placeholder="请输入参考物实际长度" />
			</view>
			
			<view class="measure-controls">
				<button class="btn small-btn" @click="startSetReference" v-if="!isSettingReference">开始设置参考点</button>
				<button class="btn small-btn danger" @click="cancelSetReference" v-if="isSettingReference">取消</button>
				<button class="btn small-btn" @click="clearAll">清除所有</button>
			</view>
		</view>
		
		<!-- 十字准星与照片显示 -->
		<view class="photo-display-section" v-if="isSettingReference || (photoData && physicalDimensions.width)">
			<view class="photo-display">
				<canvas 
					canvas-id="photoCanvas" 
					id="photoCanvas"
					class="photo-canvas"
					:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
					@tap="onCanvasTap"
				></canvas>
				<view class="crosshair-overlay" v-if="currentCrosshair && isSettingReference">
					<view class="crosshair-horizontal" :style="{top: currentCrosshair.y + 'px'}"></view>
					<view class="crosshair-vertical" :style="{left: currentCrosshair.x + 'px'}"></view>
				</view>
			</view>
			
			<!-- 微调控制面板 -->
			<view v-if="isSettingReference">
				<view class="direction-buttons">
					<button class="direction-btn up" @click="moveCrosshair('up')">↑</button>
					<view class="middle-row">
						<button class="direction-btn left" @click="moveCrosshair('left')">←</button>
						<button class="direction-btn confirm" @click="confirmReferencePoint">确定</button>
						<button class="direction-btn right" @click="moveCrosshair('right')">→</button>
					</view>
					<button class="direction-btn down" @click="moveCrosshair('down')">↓</button>
				</view>
				
				<view class="status-text">
					<text>正在设置参考点 {{ settingPointIndex }}/2</text>
				</view>
			</view>
		</view>
		
		<!-- 测量结果显示 -->
		<view class="result-section" v-if="physicalDimensions.width">
			<text class="result-title">测量结果:</text>
			<view class="result-item">
				<text>照片物理宽度: {{ physicalDimensions.width.toFixed(1) }} mm</text>
			</view>
			<view class="result-item">
				<text>照片物理高度: {{ physicalDimensions.height.toFixed(1) }} mm</text>
			</view>
			<text class="accuracy-tip">
				* 提示: 结果的准确性取决于参考物长度是否精确，以及拍照时是否正对物体。
			</text>
		</view>
		
		<!-- 操作提示 -->
		<view class="tips">
			<text>提示：拍照后，设置一个已知长度的参考物，程序将自动计算照片的物理尺寸。</text>
		</view>
	</view>
</template>

<script>
import TakePhoto from '@/components/TakePhoto/TakePhoto.vue';

export default {
	components: {
		TakePhoto
	},
	data() {
		return {
			photoData: '',
			referenceLength: 50, // 默认参考物长度，单位mm
			physicalDimensions: {
				width: null,
				height: null,
			},
			canvasWidth: 300,
			canvasHeight: 400,
			referencePoints: [], // 参考物的两个端点
			scaleFactor: 0, // 像素到实际尺寸的转换比例
			isSettingReference: false,
			currentCrosshair: null, // 十字准星位置
			settingPointIndex: 0,
		}
	},
	methods: {
		takePhoto() {
			this.$refs.takePhoto.takePhoto();
		},
		
		onPhotoTaken(photoData) {
			// 1. 重置旧状态
			this.referencePoints = [];
			this.physicalDimensions = { width: null, height: null };
			this.scaleFactor = 0;
			this.isSettingReference = false;
			this.settingPointIndex = 0;
			
			// 2. 设置新照片
			this.photoData = photoData;
	
			// 3. 获取新照片信息
			uni.getImageInfo({
				src: photoData,
				success: (res) => {
					this.canvasWidth = res.width;
					this.canvasHeight = res.height;
					this.currentCrosshair = { 
						x: Math.floor(res.width / 2), 
						y: Math.floor(res.height / 2) 
					};
				}
			});
		},
		
		drawPhotoToCanvas() {
			if (!this.photoData) return;
			const photoCtx = uni.createCanvasContext('photoCanvas', this);
			photoCtx.drawImage(this.photoData, 0, 0, this.canvasWidth, this.canvasHeight);
			photoCtx.draw();
		},
		
		startSetReference() {
			this.isSettingReference = true;
			this.settingPointIndex = 1;
			this.physicalDimensions = { width: null, height: null }; // 重置结果
			this.referencePoints = [];
			this.currentCrosshair = { 
				x: Math.floor(this.canvasWidth / 2), 
				y: Math.floor(this.canvasHeight / 2) 
			};
			this.$nextTick(() => {
				this.drawPhotoToCanvas();
			});
		},
		
		cancelSetReference() {
			this.isSettingReference = false;
			this.settingPointIndex = 0;
			this.referencePoints = [];
			// 如果已有结果，则重新绘制带参考线的照片，否则清空
			if (this.physicalDimensions.width) {
				this.drawReferenceLine();
			} else {
				this.drawPhotoToCanvas();
			}
		},
		
		onCanvasTap(e) {
			if (!this.isSettingReference) return;
			
			const query = uni.createSelectorQuery().in(this);
			query.select('#photoCanvas').boundingClientRect();
			query.exec(res => {
				const canvasRect = res[0];
				if (canvasRect) {
					const x = Math.round(e.detail.x - canvasRect.left);
					const y = Math.round(e.detail.y - canvasRect.top);
					this.currentCrosshair.x = Math.max(0, Math.min(this.canvasWidth, x));
					this.currentCrosshair.y = Math.max(0, Math.min(this.canvasHeight, y));
				}
			});
		},
		
		moveCrosshair(direction) {
			if (!this.currentCrosshair) return;
			const step = 5;
			switch(direction) {
				case 'up':
					this.currentCrosshair.y = Math.max(0, this.currentCrosshair.y - step);
					break;
				case 'down':
					this.currentCrosshair.y = Math.min(this.canvasHeight, this.currentCrosshair.y + step);
					break;
				case 'left':
					this.currentCrosshair.x = Math.max(0, this.currentCrosshair.x - step);
					break;
				case 'right':
					this.currentCrosshair.x = Math.min(this.canvasWidth, this.currentCrosshair.x + step);
					break;
			}
		},
		
		confirmReferencePoint() {
			if (this.settingPointIndex <= 2) {
				this.referencePoints.push({...this.currentCrosshair});
				
				if (this.settingPointIndex === 1) {
					this.settingPointIndex = 2;
					uni.showToast({ title: '已设置第1个点，请设置第2个', icon: 'none' });
				} else {
					this.isSettingReference = false;
					
					const refDistance = this.calculateDistance(this.referencePoints[0], this.referencePoints[1]);
					if (refDistance === 0 || !this.referenceLength) {
						uni.showToast({ title: '参考距离或长度不能为0', icon: 'error' });
						return;
					}
					
					this.scaleFactor = this.referenceLength / refDistance;
					
					this.physicalDimensions.width = this.canvasWidth * this.scaleFactor;
					this.physicalDimensions.height = this.canvasHeight * this.scaleFactor;
					
					this.drawReferenceLine();
					
					uni.showToast({ title: '尺寸计算完成', icon: 'success' });
				}
			}
		},
		
		calculateDistance(point1, point2) {
			const dx = point2.x - point1.x;
			const dy = point2.y - point1.y;
			return Math.sqrt(dx * dx + dy * dy);
		},
		
		drawReferenceLine() {
			if (this.referencePoints.length < 2) return;
			
			const photoCtx = uni.createCanvasContext('photoCanvas', this);
			
			// 1. 重新绘制照片
			photoCtx.drawImage(this.photoData, 0, 0, this.canvasWidth, this.canvasHeight);
			
			// 2. 绘制参考线
			photoCtx.setStrokeStyle('#007AFF');
			photoCtx.setLineWidth(3);
			photoCtx.beginPath();
			photoCtx.moveTo(this.referencePoints[0].x, this.referencePoints[0].y);
			photoCtx.lineTo(this.referencePoints[1].x, this.referencePoints[1].y);
			photoCtx.stroke();
			
			const midX = (this.referencePoints[0].x + this.referencePoints[1].x) / 2;
			const midY = (this.referencePoints[0].y + this.referencePoints[1].y) / 2;
			
			photoCtx.setFillStyle('#007AFF');
			photoCtx.setFontSize(14);
			photoCtx.setTextAlign('center');
			photoCtx.fillText(`参考: ${this.referenceLength}mm`, midX, midY - 15);
			
			photoCtx.draw();
		},
		
		clearAll() {
			this.photoData = '';
			this.referencePoints = [];
			this.physicalDimensions = { width: null, height: null };
			this.scaleFactor = 0;
			this.isSettingReference = false;
			this.settingPointIndex = 0;
			
			const photoCtx = uni.createCanvasContext('photoCanvas', this);
			photoCtx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
			photoCtx.draw();
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}


.controls {
	margin: 30rpx 0;
	text-align: center;
}

.btn {
	background-color: #007AFF;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 10rpx;
	font-size: 32rpx;
	margin: 10rpx;
}

.small-btn {
	padding: 15rpx 30rpx;
	font-size: 28rpx;
	margin: 5rpx;
}

.danger {
	background-color: #FF5252;
}

.reference-section {
	background-color: white;
	padding: 30rpx;
	border-radius: 10rpx;
	margin: 30rpx 0;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.input-group {
	margin-bottom: 20rpx;
}

.label {
	display: block;
	margin-bottom: 10rpx;
	font-size: 28rpx;
	color: #666;
}

.input {
	width: 100%;
	padding: 15rpx;
	border: 1rpx solid #ddd;
	border-radius: 5rpx;
	font-size: 28rpx;
}

.result-section {
	background-color: white;
	padding: 30rpx;
	border-radius: 10rpx;
	margin: 30rpx 0;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.result-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

.result-item {
	padding: 15rpx;
	background-color: #f9f9f9;
	margin-bottom: 10rpx;
	border-radius: 5rpx;
}

.accuracy-tip {
	display: block;
	margin-top: 20rpx;
	font-size: 24rpx;
	color: #999;
}

.photo-display-section {
	margin: 30rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.photo-display {
	position: relative;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	overflow: hidden;
}

.photo-canvas {
	display: block;
}

.crosshair-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

.crosshair-horizontal {
	position: absolute;
	left: 0;
	right: 0;
	height: 1rpx;
	background-color: #FF5722;
	opacity: 0.8;
}

.crosshair-vertical {
	position: absolute;
	top: 0;
	bottom: 0;
	width: 1rpx;
	background-color: #FF5722;
	opacity: 0.8;
}

.direction-buttons {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin: 20rpx 0;
}

.direction-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background-color: #007AFF;
	color: white;
	font-size: 32rpx;
	margin: 5rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.middle-row {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.confirm {
	background-color: #4CAF50;
}

.measure-controls {
	margin: 20rpx 0;
	text-align: center;
}

.status-text {
	margin: 15rpx 0;
	text-align: center;
	font-size: 28rpx;
	color: #666;
}

.tips {
	background-color: #FFF3CD;
	padding: 20rpx;
	border-radius: 10rpx;
	margin: 30rpx 0;
	font-size: 24rpx;
	color: #856404;
	line-height: 1.5;
}
</style>
